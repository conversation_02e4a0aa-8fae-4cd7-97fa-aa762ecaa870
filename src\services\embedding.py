"""
Vector embeddings generation using OpenAI or sentence-transformers.
This module provides functions for generating vector embeddings for survey responses.
"""

import os
from typing import List, Dict, Any
import numpy as np
from openai import OpenAI

# Initialize the OpenAI client
client = OpenAI(api_key=os.environ.get("OPENAI_API_KEY"))

class EmbeddingService:
    """Service for generating vector embeddings for survey responses."""

    def __init__(self, model: str = "text-embedding-3-small"):
        """
        Initialize the embedding service.

        Args:
            model: The OpenAI embedding model to use
        """
        self.model = model

    def generate_embedding(self, text: str) -> List[float]:
        """
        Generate a vector embedding for the given text.

        Args:
            text: The text to generate an embedding for

        Returns:
            A list of floats representing the embedding vector
        """
        try:
            response = client.embeddings.create(
                model=self.model,
                input=text
            )
            return response.data[0].embedding
        except Exception as e:
            print(f"Error generating embedding: {str(e)}")
            # Return a zero vector as a fallback
            return [0.0] * 1536  # Default dimension for text-embedding-3-small

    def generate_response_embedding(self, response: Dict[str, Any]) -> List[float]:
        """
        Generate a vector embedding for a survey response.

        Args:
            response: The survey response to generate an embedding for

        Returns:
            A list of floats representing the embedding vector
        """
        # Concatenate all the answers into a single text
        text = ""

        # Add demographic information if available
        if "age" in response and response["age"] is not None:
            text += f"Age: {response['age']}. "

        if "region" in response and response["region"] is not None:
            text += f"Region: {response['region']}. "

        # Add all question-answer pairs
        for answer in response.get("answers", []):
            if isinstance(answer, dict):
                question = answer.get("question", "")
                answer_text = answer.get("answer", "")
                text += f"Question: {question} Answer: {answer_text}. "

        # Generate the embedding
        return self.generate_embedding(text)

    def calculate_similarity(self, embedding1: List[float], embedding2: List[float]) -> float:
        """
        Calculate the cosine similarity between two embeddings.

        Args:
            embedding1: The first embedding vector
            embedding2: The second embedding vector

        Returns:
            A float between -1 and 1 representing the cosine similarity
        """
        # Convert to numpy arrays
        vec1 = np.array(embedding1)
        vec2 = np.array(embedding2)

        # Calculate cosine similarity
        dot_product = np.dot(vec1, vec2)
        norm1 = np.linalg.norm(vec1)
        norm2 = np.linalg.norm(vec2)

        if norm1 == 0 or norm2 == 0:
            return 0.0

        return dot_product / (norm1 * norm2)

# Create a singleton instance of the embedding service
embedding_service = EmbeddingService()
