# Environment Variables Setup Guide

This guide shows you how to set up environment variables for your Survey Insight System.

## Method 1: Using .env file (Recommended for Development)

### Step 1: Create/Update .env file
Your `.env` file already exists with the following content:
```
OPENAI_API_KEY=***************************************************
DATABASE_URL=**************************************/surveydb
MONGO_URI=mongodb+srv://tuanminhaj34:<EMAIL>/?retryWrites=true&w=majority&appName=Survey-Analyzer
GOOGLE_API_KEY=AIzaSyBT-0ZLL29CH_dahc1OR7SUwdVVAceAH8M
```

### Step 2: Install python-dotenv (if not already installed)
```bash
pip install python-dotenv
```

### Step 3: Your app_oop.py is already configured to load .env
The code automatically loads environment variables:
```python
from dotenv import load_dotenv
load_dotenv()
```

## Method 2: System Environment Variables (Windows)

### Option A: Command Prompt (Temporary - current session only)
```cmd
set OPENAI_API_KEY=your_openai_api_key_here
set MONGO_URI=your_mongodb_uri_here
streamlit run app_oop.py
```

### Option B: PowerShell (Temporary - current session only)
```powershell
$env:OPENAI_API_KEY="your_openai_api_key_here"
$env:MONGO_URI="your_mongodb_uri_here"
streamlit run app_oop.py
```

### Option C: Windows System Environment Variables (Permanent)
1. Press `Win + R`, type `sysdm.cpl`, press Enter
2. Click "Environment Variables" button
3. Under "User variables" or "System variables", click "New"
4. Variable name: `OPENAI_API_KEY`
5. Variable value: `your_openai_api_key_here`
6. Click OK
7. Restart your command prompt/IDE

## Method 3: Streamlit Secrets (For Streamlit Cloud Deployment)

### Create .streamlit/secrets.toml
```toml
OPENAI_API_KEY = "your_openai_api_key_here"
MONGO_URI = "your_mongodb_uri_here"
GOOGLE_API_KEY = "your_google_api_key_here"
```

### Access in code:
```python
import streamlit as st
api_key = st.secrets["OPENAI_API_KEY"]
```

## Method 4: Docker Environment Variables

### In Dockerfile:
```dockerfile
ENV OPENAI_API_KEY=your_openai_api_key_here
ENV MONGO_URI=your_mongodb_uri_here
```

### Or with docker run:
```bash
docker run -e OPENAI_API_KEY=your_key -e MONGO_URI=your_uri your_image
```

### Or with docker-compose.yml:
```yaml
version: '3.8'
services:
  app:
    build: .
    environment:
      - OPENAI_API_KEY=your_openai_api_key_here
      - MONGO_URI=your_mongodb_uri_here
    ports:
      - "7860:7860"
```

## Method 5: Hugging Face Spaces Environment Variables

### For Hugging Face deployment:
1. Go to your Space settings
2. Click on "Variables and secrets"
3. Add environment variables:
   - Name: `OPENAI_API_KEY`
   - Value: `your_openai_api_key_here`
4. Click "Save"

## Security Best Practices

### ✅ DO:
- Use `.env` files for local development
- Add `.env` to your `.gitignore` file
- Use secrets management for production
- Rotate API keys regularly
- Use different keys for different environments

### ❌ DON'T:
- Commit API keys to version control
- Share API keys in plain text
- Use production keys in development
- Hard-code API keys in your source code

## Verification

### Test if environment variables are loaded:
```python
import os
from dotenv import load_dotenv

load_dotenv()

# Check if API key is loaded
api_key = os.environ.get("OPENAI_API_KEY")
if api_key:
    print("✅ OpenAI API key loaded successfully")
    print(f"Key starts with: {api_key[:10]}...")
else:
    print("❌ OpenAI API key not found")
```

## Troubleshooting

### Issue: "No OpenAI API key found"
**Solutions:**
1. Check if `.env` file exists in the project root
2. Verify the variable name is exactly `OPENAI_API_KEY`
3. Ensure no spaces around the `=` sign
4. Restart your IDE/terminal after setting system variables
5. Check if `python-dotenv` is installed

### Issue: API key not working
**Solutions:**
1. Verify the API key is valid on OpenAI platform
2. Check if you have sufficient credits
3. Ensure the key has the right permissions
4. Try regenerating the API key

## Current Status
✅ Your `.env` file is already configured
✅ Your `app_oop.py` loads environment variables automatically
✅ Ready to run: `streamlit run app_oop.py`
