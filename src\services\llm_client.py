from openai import OpenAI
import os
import json
import logging
from dotenv import load_dotenv
from app.exceptions import ConfigurationException, LLMException

# Configure logger
logger = logging.getLogger(__name__)

load_dotenv()

# Check for API key
api_key = os.getenv("OPENAI_API_KEY")
if not api_key:
    raise ConfigurationException(
        detail="OPENAI_API_KEY is not set in the environment or .env file.",
        field="OPENAI_API_KEY",
        suggestion="Please set the OPENAI_API_KEY environment variable or add it to your .env file."
    )

# Initialize OpenAI client
client = OpenAI(api_key=api_key)

def generate_questions_from_llm(
    context: str,
    language: str,
    num_questions: int,
    messages: list = None
) -> list[str]:
    """Generate survey questions using OpenAI's LLM.

    Args:
        context: The context for the survey questions
        language: The language to generate questions in
        num_questions: The number of questions to generate
        messages: Optional list of previous messages for context

    Returns:
        A list of generated questions

    Raises:
        LLMException: If there's an error with the LLM request or response
    """
    # Create the prompt for generating questions
    prompt = f"""
You are a survey question generator. Generate {num_questions} brief and relevant survey questions in {language} based on the following context:

Context: \"{context}\"

Return the result as a JSON object with a key 'questions' and a list of question strings.
"""

    try:
        # Log the request
        logger.info(f"Sending request to OpenAI for {num_questions} questions in {language}")

        # Prepare messages for the API call
        if messages:
            # If we have previous messages, add the new prompt as the latest user message
            api_messages = messages.copy()
            api_messages.append({"role": "user", "content": prompt})
            logger.info(f"Using conversation history with {len(api_messages)} messages")
        else:
            # If no previous messages, create a new conversation
            api_messages = [
                {"role": "system", "content": "You generate relevant survey questions in JSON."},
                {"role": "user", "content": prompt}
            ]
            logger.info("Starting new conversation")

        # Make the API call
        response = client.chat.completions.create(
            model="gpt-4.1",
            messages=api_messages,
            temperature=0.5,
            max_tokens=600
        )

        # Parse the response
        content = response.choices[0].message.content

        # Validate JSON format
        try:
            questions_json = json.loads(content) if isinstance(content, str) else content
        except json.JSONDecodeError as e:
            raise LLMException(
                detail="Failed to parse LLM response as JSON.",
                field="response",
                suggestion="The LLM returned an invalid JSON response. Please try again or contact support."
            )

        # Validate response structure
        if not isinstance(questions_json, dict) or "questions" not in questions_json:
            raise LLMException(
                detail="Malformed LLM response. 'questions' key not found.",
                field="questions",
                suggestion="The LLM response is missing the required 'questions' field. Please try again with a different prompt."
            )

        # Return the questions
        questions = questions_json.get("questions", [])
        logger.info(f"Successfully generated {len(questions)} questions")
        return questions

    except LLMException:
        # Re-raise our custom exceptions
        raise
    except Exception as e:
        # Convert other exceptions to LLMException
        logger.exception("Error in LLM request")
        raise LLMException(
            detail=f"LLM generation failed: {str(e)}",
            field="",
            suggestion="Please check your API key and network connection, then try again."
        )
