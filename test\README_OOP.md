# Survey Insight System (OOP Version)

A comprehensive, object-oriented survey management system with both UI and API interfaces.

## 🎯 Features

### 🖥️ **Dual Interface Support**
- **UI Interface** - Interactive Streamlit web application
- **API Documentation** - Complete FastAPI documentation and testing

### 📊 **Core Functionality**
1. **Question Generator** - AI-powered survey question generation
2. **Response Generator** - Collect and manage survey responses
3. **View Stored Responses** - Search, filter, and export responses
4. **Survey Insights** - AI-powered analysis and insights generation

### 🏗️ **Clean Architecture**
- Object-Oriented Programming (OOP) design
- Modular, maintainable, and scalable code
- Separation of concerns
- Easy to extend and test

## 🚀 Quick Start

### Prerequisites
```bash
pip install -r requirements.txt
```

### Environment Setup
1. Copy your environment variables:
```bash
# Your .env file should contain:
OPENAI_API_KEY=your_openai_api_key_here
MONGO_URI=your_mongodb_uri_here
GOOGLE_API_KEY=your_google_api_key_here
```

2. Test your environment:
```bash
python test_env.py
```

### Running the Application

#### Option 1: UI Only (Streamlit)
```bash
streamlit run app_oop.py
```
or
```bash
./start.sh ui-only
```

#### Option 2: API Only (FastAPI)
```bash
uvicorn app.main:app --host 0.0.0.0 --port 8000
```
or
```bash
./start.sh api-only
```

#### Option 3: Both UI and API (Recommended)
```bash
./start.sh
```

## 🌐 Access Points

| Service | URL | Description |
|---------|-----|-------------|
| **Streamlit UI** | http://localhost:7860 | Main web interface |
| **FastAPI Server** | http://localhost:8000 | REST API endpoints |
| **API Documentation** | http://localhost:8000/docs | Interactive API docs |
| **API Testing** | http://localhost:7860 → API Documentation | Built-in API testing |

## 📱 Interface Overview

### UI Interface
The Streamlit interface provides four main tabs:

1. **Question Generator**
   - Generate AI-powered survey questions
   - Support for 30+ languages
   - Session management for continued conversations
   - Intelligent question count detection

2. **Response Generator**
   - Submit survey responses
   - Demographic information collection
   - Auto-save functionality
   - JSON export capabilities

3. **View Stored Responses**
   - Search and filter responses
   - Export to JSON
   - Load from local files
   - Response analytics

4. **Survey Insights**
   - AI-powered insights generation
   - Multiple LLM providers (OpenAI, Gemini)
   - Parameter optimization
   - MongoDB integration
   - Example queries for different survey types

### API Documentation Interface
The API documentation tab provides:

1. **Question Generator API**
   - Complete endpoint documentation
   - Request/response examples
   - Interactive testing

2. **Response Generator API**
   - Submit and retrieve responses
   - Full CRUD operations
   - Authentication examples

3. **Insights API**
   - Generate insights from survey data
   - Advanced query capabilities
   - Model parameter control

4. **API Testing**
   - Live API testing interface
   - Pre-filled examples
   - Real-time response display

## 🔧 API Endpoints

### Authentication
All API endpoints require authentication via API key:
```bash
X-API-Key: survey-insight-system-token-2025
```

### Core Endpoints

#### Generate Questions
```bash
POST /generate/
Content-Type: application/json

{
  "context": "Create a customer satisfaction survey",
  "language": "English",
  "num_questions": 5,
  "session_id": "session-12345678"
}
```

#### Submit Response
```bash
POST /responses/submit
Content-Type: application/json

{
  "survey_id": "session-12345678",
  "user_id": "user-987654",
  "age": 28,
  "region": "New York",
  "answers": [
    {
      "question": "How satisfied are you?",
      "answer": "Very satisfied"
    }
  ]
}
```

#### Generate Insights
```bash
POST /insights/generate
Content-Type: application/json

{
  "survey_id": "survey_341",
  "user_query": "Summarize user satisfaction levels",
  "llm_provider": "OpenAI",
  "model_params": {
    "model": "gpt-4o",
    "temperature": 0.3,
    "max_tokens": 1500
  }
}
```

## 🏗️ Architecture

### Class Structure
```
SurveyInsightApp (Main Application)
├── OpenAIClient (AI Integration)
├── SessionManager (State Management)
├── FileManager (File Operations)
└── Tabs (UI Components)
    ├── QuestionGeneratorTab
    ├── ResponseGeneratorTab
    ├── ViewResponsesTab
    └── SurveyInsightsTab
```

### Key Components

- **Config** - Centralized configuration
- **OpenAIClient** - AI model interactions
- **SessionManager** - Session state management
- **FileManager** - File I/O operations
- **BaseTab** - Abstract tab interface
- **SurveyInsightApp** - Main application orchestrator

## 🔒 Security Features

- API key authentication
- Environment variable protection
- Input validation
- Error handling
- Rate limiting ready

## 📊 Supported Survey Types

| Survey ID | Topic | Collection |
|-----------|-------|------------|
| survey_341 | Teaching Quality | teaching_quality |
| survey_342 | Electric Vehicles | ev_survey_2025 |
| survey_343 | AI Tools | AI_tools |
| survey_344 | Online Payments | online_payments |
| survey_345 | Remote Work | remore_work |

## 🛠️ Development

### Adding New Features
1. Create new tab class inheriting from `BaseTab`
2. Implement the `render()` method
3. Add to `SurveyInsightApp` initialization
4. Update tab selection logic

### Testing
```bash
# Test environment variables
python test_env.py

# Test API endpoints
# Use the built-in API testing interface in the UI
```

## 🐳 Docker Deployment

### Build and Run
```bash
docker build -t survey-insight-oop .
docker run -p 7860:7860 -p 8000:8000 survey-insight-oop
```

### Environment Variables in Docker
```bash
docker run -e OPENAI_API_KEY=your_key -e MONGO_URI=your_uri survey-insight-oop
```

## 📈 Benefits of OOP Version

✅ **Maintainable** - Clean separation of concerns
✅ **Scalable** - Easy to add new features
✅ **Testable** - Each component can be unit tested
✅ **Professional** - Follows software engineering best practices
✅ **Dual Interface** - Both UI and API in one application
✅ **Comprehensive** - Complete feature parity with original
✅ **Documentation** - Built-in API documentation and testing

## 🤝 Contributing

1. Follow the OOP patterns established
2. Add proper type hints
3. Include docstrings for all methods
4. Test both UI and API functionality
5. Update documentation as needed

## 📝 License

This project is part of the Survey Insight System suite.
