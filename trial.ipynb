import os
import openai

def is_query_relevant_to_context(query: str, context: str) -> bool:
    """
    Uses an LLM system prompt to determine if a user query is relevant to the provided survey context.
    Returns True if the query is relevant, False otherwise.
    Falls back to conservative pattern matching if LLM is unavailable.
    """
    try:
        openai_client = openai.OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        system_prompt = (
            "You are an expert at classifying survey insight queries. "
            "Given a user query and a survey context, determine if the query is relevant to the context. "
            "If the query is relevant and specific to the context, respond with 'YES'. "
            "If the query is generic, irrelevant, or not related to the context, respond with 'NO'. "
            "Respond with only 'YES' or 'NO'."
        )
        response = openai_client.chat.completions.create(
            model="gpt-4",
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": f"Survey Context: {context}\nQuery: {query}"}
            ],
            temperature=0,
            max_tokens=3
        )
        answer = response.choices[0].message.content.strip().upper()
        return answer == "YES"
    except Exception:
        # Fallback to conservative pattern matching
        query_lower = query.lower().strip()
        context_lower = context.lower().strip()
        if len(query_lower) < 3:
            return False
        if query_lower in ["test", "testing", "this is a test", "hello", "hi", "check", "sample", "example", "demo", "trial"]:
            return False
        # If any word from context appears in query, consider it relevant
        context_words = set(context_lower.split())
        query_words = set(query_lower.split())
        if context_words & query_words:
            return True
        return False


is_query_relevant_to_context(query="Summarize how users aged 18-25 in New York responded to questions about teaching quality.", context="teaching_quality")

import os

api_key = os.getenv("GOOGLE_API_KEY")
if api_key:
    print("GOOGLE_API_KEY is set.")
else:
    print("GOOGLE_API_KEY is not set or is empty.")

import requests

# Example Gemini API call using the provided api_key
# Try with other Gemini models
# Example: gemini-pro-vision
url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent"
headers = {"Content-Type": "application/json"}
data = {
    "contents": [
        {"parts": [{"text": "Hello, Gemini! How can you help me today?"}]}
    ]
}
params = {"key": api_key}

response = requests.post(url, headers=headers, params=params, json=data)
print(response.json())

from pymongo import MongoClient
import os

# Connect to MongoDB
client = MongoClient(os.getenv("MONGO_URI"))
collection = client["survey_dev_db"]["real_health_lifestyle"]

# Your filter input
filters = {
"age_min":18,
"age_max":25,
"region":"Trivendrum "
}

# Build MongoDB query using dot notation
query = {
    "demographics.age": {
        "$gte": filters["age_min"],
        "$lte": filters["age_max"]
    },
    "geographics.location": filters["region"]
}

# Query the collection
results = list(collection.find(query))

# Output result count
print(f"Found {len(results)} records within age range {filters['age_min']}–{filters['age_max']}")


# Fix region spelling and strip spaces
region = filters["region"].strip()  # Remove leading/trailing spaces

# Optionally, correct spelling if needed
region = region.replace("Trivandrum", "Trivendrum")

query = {
    "demographics.age": {
        "$gte": filters["age_min"],
        "$lte": filters["age_max"]
    },
    "geographics.location": region
}

results = list(collection.find(query))
print(f"Found {len(results)} records within age range {filters['age_min']}–{filters['age_max']} and location '{region}'")

results

#datasets
EU LGBT Survey - https://www.kaggle.com/datasets/ruslankl/european-union-lgbt-survey-2012
Starbucks Customer Survey - https://www.kaggle.com/datasets/mahirahmzh/starbucks-customer-retention-malaysia-survey?select=Starbucks+satisfactory+survey.csv
Mental Health in Tech Survey - https://www.kaggle.com/datasets/osmi/mental-health-in-tech-survey
Music & Mental Health Survey Results -https://www.kaggle.com/datasets/catherinerasgaitis/mxmh-survey-results
Stack Overflow 2018 Developer Survey - https://www.kaggle.com/datasets/stackoverflow/stack-overflow-2018-developer-survey
Data Hackers Survey 2019-2020 - https://www.kaggle.com/datasets/datahackers/pesquisa-data-hackers-2019?select=datahackers-survey-2019-anonymous-responses.csv (multilingual data)
Student Mental Health Survey - https://www.kaggle.com/datasets/abdullahashfaqvirk/student-mental-health-survey
Student Feedback Survey Responses - https://www.kaggle.com/datasets/ruchi798/student-feedback-survey-responses?resource=download
Health and Lifestyle Habits of Students - https://www.kaggle.com/datasets/nannamsaidivyanaina/health-and-lifestyle-habits-of-students

!pip install openpyxl
import pandas as pd

df = pd.read_csv("artifacts\\EU LGBT Survey\\LGBT_Survey_Discrimination.csv")
df

df.question_label.nunique()

import json

# Convert df to JSON-like records, ignoring the first column
json_records = df.to_dict(orient='records')
print(json.dumps(json_records[:5], indent=2, ensure_ascii=False))  # Show first 5 as example

for i, rec in enumerate(json_records):
    rec['user_id'] = f"user_{i}"
# json_records[:5]  # Show first 5 as example
for rec in json_records:
    rec["survey_id"] = "survey_546"
# Rename 'CountryCode' to 'location' in each record
for rec in json_records:
    rec['location'] = rec.pop('CountryCode')
    rec['question'] = rec.pop('question_label')
transformed_records = json_records

import json

# Transform json_records to new schema with user_id and survey_id, splitting questions/answers as fields
transformed_records = []
for rec in json_records:
    user_id = rec['Student ID']
    for k, v in rec.items():
        if k != "Student ID":
            record = {
                "survey_id": "survey_546",
                "user_id": user_id,
                "question": k,
                "answer": v
            }
            transformed_records.append(record)

print(json.dumps(transformed_records, indent=2, ensure_ascii=False))  # Show first 5 as example

transformed_records = json_records
len(transformed_records)

# Get only 100 unique user_id records from transformed_records
unique_users = set()
filtered_records = []
for rec in transformed_records:
    uid = rec['user_id']
    if uid not in unique_users:
        unique_users.add(uid)
        filtered_records.append(rec)
    if len(unique_users) == 100:
        break

print(f"Number of unique user_id records: {len(filtered_records)}")
filtered_records[:5]  # Show first 5 as example

from collections import Counter

user_id_counts = Counter(rec['user_id'] for rec in transformed_records[:800])
print(user_id_counts)

df











import json

# Example schema transformation for the first 5 records
records = []
for idx, row in df.iterrows():
    for col in df.columns:
        if col not in ["Location", "Age:"]:
            record = {
                "survey_id": "survey_health_lifestyle",
                "user_id": f"user_{idx}",
                "demographics": {
                    "age": row["Age:"]
                },
                "geographics": {
                    "location": row["Location"]
                },
                
                "question": col,
                "answer": row[col]
                
            }
            records.append(record)

# Example: print the first 5 records as JSON
print(json.dumps(records, indent=2, ensure_ascii=False))


for rec in records:
    rec['survey_id'] = "survey_544"
records

len(transformed_records)

from pymongo import MongoClient
import os

# Connect to MongoDB
client = MongoClient(os.getenv("MONGO_URI"))
collection = client["survey_dev_db"]["real_eu_LGBT"]

# Insert records into the collection
if transformed_records:
    collection.insert_many(transformed_records[:800])
    print(f"Inserted {len(transformed_records)} records into MongoDB collection.")
else:
    print("No records to insert.")

# Remove all documents from the MongoDB collection
delete_result = collection.delete_many({})
print(f"Deleted {delete_result.deleted_count} documents from the collection.")

user_query = "Show the most common types of discrimination experienced by gay men in Belgium according to the EU LGBT Survey."
Show the most common types of discrimination experienced by gay men in Belgium.
user_query



SURVEY_COLLECTIONS = {
    "survey_342": "ev_survey_2025",
    "survey_345": "remore_work",
    "survey_343": "AI_tools",
    "survey_341": "teaching_quality",
    "survey_344": "online_payments"
}

# Default collection name if survey_id is not in the mapping
DEFAULT_COLLECTION = "teaching_quality"
def get_collection_for_survey(survey_id: str) -> str:
    """
    Get the appropriate MongoDB collection name for a given survey_id.

    Args:
        survey_id: The survey ID to look up

    Returns:
        The collection name to use for this survey
    """
    # Reverse the mapping to get collection name from survey_id
    return SURVEY_COLLECTIONS.get(survey_id, DEFAULT_COLLECTION)

get_collection_for_survey("survey_544")