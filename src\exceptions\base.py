from typing import Dict, Any


class SurveyInsightException(Exception):
    """Base exception class for Survey Insight System.

    All custom exceptions in the application should inherit from this class.
    """

    def __init__(
        self,
        status_code: int = 500,
        key: str = "internal_error",
        detail: str = "An internal server error occurred.",
        field: str = "",
        suggestion: str = "Please try again later or contact support."
    ):
        """Initialize the base exception.

        Args:
            status_code: HTTP status code to return
            key: Machine-readable error code for client handling
            detail: Human-readable error message
            field: The field that caused the error (if applicable)
            suggestion: Suggested action to resolve the error
        """
        self.status_code = status_code
        self.key = key
        self.detail = detail
        self.field = field
        self.suggestion = suggestion

        # Use the detail as the exception message
        super().__init__(self.detail)

    def to_dict(self) -> Dict[str, Any]:
        """Convert the exception to a dictionary for JSON response."""
        return {
            "error": {
                "status_code": self.status_code,
                "key": self.key,
                "detail": self.detail,
                "field": self.field,
                "suggestion": self.suggestion
            }
        }
