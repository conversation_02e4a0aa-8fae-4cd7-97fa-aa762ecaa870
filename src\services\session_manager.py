import uuid
import time
from typing import Dict, List, Any, Optional
import logging

# Configure logger
logger = logging.getLogger(__name__)

class SessionManager:
    """Manages conversation sessions for maintaining context between requests."""
    
    def __init__(self, session_timeout_seconds: int = 3600):
        """Initialize the session manager.
        
        Args:
            session_timeout_seconds: Time in seconds after which a session expires
        """
        self.sessions: Dict[str, Dict[str, Any]] = {}
        self.session_timeout_seconds = session_timeout_seconds
    
    def create_session(self, context: str, language: str) -> str:
        """Create a new session.
        
        Args:
            context: The initial context for the session
            language: The language for the session
            
        Returns:
            The session ID
        """
        session_id = f"session-{uuid.uuid4().hex[:8]}"
        self.sessions[session_id] = {
            "created_at": time.time(),
            "last_accessed": time.time(),
            "language": language,
            "context": context,
            "messages": [
                {"role": "system", "content": "You generate relevant survey questions in JSON."},
                {"role": "user", "content": f"Context: {context}"}
            ],
            "questions": []
        }
        logger.info(f"Created new session: {session_id}")
        return session_id
    
    def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get a session by ID.
        
        Args:
            session_id: The session ID
            
        Returns:
            The session data or None if not found
        """
        session = self.sessions.get(session_id)
        if not session:
            logger.warning(f"Session not found: {session_id}")
            return None
        
        # Check if session has expired
        if time.time() - session["last_accessed"] > self.session_timeout_seconds:
            logger.info(f"Session expired: {session_id}")
            del self.sessions[session_id]
            return None
        
        # Update last accessed time
        session["last_accessed"] = time.time()
        return session
    
    def update_session(self, session_id: str, new_context: str, questions: List[str]) -> None:
        """Update a session with new context and questions.
        
        Args:
            session_id: The session ID
            new_context: The new context to add
            questions: The newly generated questions
        """
        session = self.get_session(session_id)
        if not session:
            logger.warning(f"Cannot update non-existent session: {session_id}")
            return
        
        # Update context
        if new_context:
            session["context"] += f"\n\nAdditional context: {new_context}"
            session["messages"].append({"role": "user", "content": f"Additional context: {new_context}"})
        
        # Add assistant response
        session["messages"].append({
            "role": "assistant", 
            "content": f"Generated questions: {', '.join(questions)}"
        })
        
        # Update questions
        session["questions"].extend(questions)
        logger.info(f"Updated session {session_id} with {len(questions)} new questions")
    
    def clean_expired_sessions(self) -> int:
        """Remove expired sessions.
        
        Returns:
            Number of sessions removed
        """
        current_time = time.time()
        expired_sessions = [
            session_id for session_id, session in self.sessions.items()
            if current_time - session["last_accessed"] > self.session_timeout_seconds
        ]
        
        for session_id in expired_sessions:
            del self.sessions[session_id]
        
        if expired_sessions:
            logger.info(f"Cleaned {len(expired_sessions)} expired sessions")
        
        return len(expired_sessions)


# Create a singleton instance
session_manager = SessionManager()
