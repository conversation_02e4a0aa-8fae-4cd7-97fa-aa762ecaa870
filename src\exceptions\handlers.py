from fastapi import Request
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from .base import SurveyInsightException
import logging

# Configure logger
logger = logging.getLogger(__name__)


async def survey_insight_exception_handler(request: Request, exc: SurveyInsightException):
    """Handle custom SurveyInsightException exceptions."""
    # Log the exception
    logger.error(
        f"Exception: {exc.key} - {exc.detail}",
        extra={
            "status_code": exc.status_code,
            "key": exc.key,
            "field": exc.field,
            "path": request.url.path
        }
    )

    # Return a JSON response with the exception details
    return JSONResponse(
        status_code=exc.status_code,
        content=exc.to_dict()
    )


async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """Handle FastAPI's RequestValidationError."""
    from .types import ValidationException

    # Extract field information if available
    field = ""
    if exc.errors() and len(exc.errors()) > 0:
        # Try to get the field name from the first error
        loc = exc.errors()[0].get("loc", [])
        if loc and len(loc) > 1:
            field = str(loc[1])  # The second element is usually the field name

    # Create a helpful suggestion based on the errors
    suggestion = "Please check the input data and try again."

    # Convert to our custom exception format
    custom_exc = ValidationException(
        detail="The request data failed validation.",
        field=field,
        suggestion=suggestion
    )

    # Log the exception
    logger.error(
        f"Validation Exception: {custom_exc.detail}",
        extra={
            "status_code": custom_exc.status_code,
            "key": custom_exc.key,
            "field": custom_exc.field,
            "errors": exc.errors(),
            "path": request.url.path
        }
    )

    # Return a JSON response with the exception details
    return JSONResponse(
        status_code=custom_exc.status_code,
        content=custom_exc.to_dict()
    )


async def general_exception_handler(request: Request, exc: Exception):
    """Handle any unhandled exceptions."""
    from .base import SurveyInsightException

    # Convert to our custom exception format
    custom_exc = SurveyInsightException(
        detail=f"An unexpected error occurred: {str(exc)}",
        field="",
        suggestion="Please try again later or contact support."
    )

    # Log the exception
    logger.error(
        f"Unhandled Exception: {str(exc)}",
        extra={
            "status_code": custom_exc.status_code,
            "key": custom_exc.key,
            "path": request.url.path
        },
        exc_info=True
    )

    # Return a JSON response with the exception details
    return JSONResponse(
        status_code=custom_exc.status_code,
        content=custom_exc.to_dict()
    )
