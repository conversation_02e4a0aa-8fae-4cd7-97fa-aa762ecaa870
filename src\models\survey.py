from pydantic import BaseModel, Field, field_validator
from typing import List, Dict, Union, Optional
from datetime import datetime
import uuid

class SurveyRequest(BaseModel):
    context: str
    language: str
    session_id: Optional[str] = Field(
        default=None,
        description="Optional session ID to maintain context between requests"
    )

class SurveyResponse(BaseModel):
    session_id: str = Field(
        description="Session ID for maintaining context in follow-up requests"
    )
    questions: List[str] = Field(
        description="List of generated survey questions"
    )
    is_continued_session: bool = Field(
        default=False,
        description="Indicates if this response is from a continued session"
    )
    metadata: Optional[Dict[str, Union[str, int]]] = Field(
        default=None,
        description="Additional metadata about the response"
    )

class SurveyAnswer(BaseModel):
    question: str = Field(
        description="The survey question being answered"
    )
    answer: str = Field(
        description="The answer provided by the respondent"
    )

class SurveyResponseSubmission(BaseModel):
    survey_id: str = Field(
        description="Unique identifier for the survey"
    )
    user_id: str = Field(
        description="Unique identifier for the user/respondent"
    )
    age: Optional[int] = Field(
        default=None,
        description="Age of the respondent"
    )
    region: Optional[str] = Field(
        default=None,
        description="Geographic region of the respondent"
    )
    answers: List[SurveyAnswer] = Field(
        description="List of question-answer pairs"
    )

    @field_validator('age')
    def validate_age(cls, v):
        if v is not None and (v < 0 or v > 120):
            raise ValueError('Age must be between 0 and 120')
        return v

class StoredSurveyResponse(BaseModel):
    id: str = Field(
        default_factory=lambda: str(uuid.uuid4()),
        description="Unique identifier for the stored response"
    )
    survey_id: str = Field(
        description="Unique identifier for the survey"
    )
    user_id: str = Field(
        description="Unique identifier for the user/respondent"
    )
    age: Optional[int] = Field(
        default=None,
        description="Age of the respondent"
    )
    region: Optional[str] = Field(
        default=None,
        description="Geographic region of the respondent"
    )
    answers: List[Dict[str, str]] = Field(
        description="List of question-answer pairs"
    )
    vector_id: Optional[str] = Field(
        default=None,
        description="ID of the vector embedding in the vector database"
    )
    created_at: datetime = Field(
        default_factory=datetime.now,
        description="Timestamp when the response was created"
    )

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }