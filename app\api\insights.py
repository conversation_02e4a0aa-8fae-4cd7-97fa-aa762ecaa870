"""
API endpoints for generating insights from survey responses.
"""

from fastapi import APIRouter, HTTPException, status, Body
from pydantic import BaseModel, Field
from typing import Dict, Any, Optional, List
import os
import json
from datetime import datetime

# Import the SurveyInsightGenerator
from app.api.survey_insight_generator import SurveyInsightGenerator
# from src.api.insights import SurveyInsightGenerator
from app.utils.examples import examples

# Survey ID to collection name mapping
SURVEY_COLLECTIONS = {
    "survey_342": "ev_survey_2025",
    "survey_345": "remore_work",
    "survey_343": "AI_tools",
    "survey_341": "teaching_quality",
    "survey_344": "online_payments",
    "survey_544": "real_health_lifestyle",
    "survey_545": "real_student_feedback",
    "survey_546": "real_eu_LGBT"
}

# Default collection name if survey_id is not in the mapping
DEFAULT_COLLECTION = "teaching_quality"

router = APIRouter(
    prefix="/insights",
    tags=["3. Insight Creator"],
    responses={404: {"description": "Not found"}},
)

class InsightRequest(BaseModel):
    """Request model for generating insights."""
    survey_id: str = Field(..., description="ID of the survey to analyze")
    user_query: str = Field(..., description="Natural language query describing the insights to generate")
    llm_provider: str = Field(..., description="LLM provider to use (OpenAI or Gemini)")
    model_name: str = Field(..., description="LLM model name to use")
    model_params: Dict[str, Any] = Field(..., description="LLM model parameters (model, temperature, max_tokens, etc.)")

class InsightResponse(BaseModel):
    """Response model for generated insights."""
    survey_id: str = Field(..., description="ID of the survey analyzed")
    query: str = Field(..., description="Original user query")
    insight: Optional[str] = Field(None, description="Generated insight text")
    error: Optional[str] = Field(None, description="Error message, if any")
    filtered_count: int = Field(..., description="Number of responses after filtering")
    total_count: int = Field(..., description="Total number of responses for the survey")
    # filters_applied: Dict[str, Any] = Field({}, description="Filters applied based on the query")
    # generated_at: Optional[str] = Field(None, description="Timestamp when the insight was generated")

# Initialize MongoDB connection parameters
MONGO_URI = os.environ.get("MONGO_URI")
DB_NAME = os.environ.get("MONGODB_DB_NAME", "survey_dev_db")
OPENAI_API_KEY = os.environ.get("OPENAI_API_KEY")

# Function to get the appropriate collection name for a survey_id
def get_collection_for_survey(survey_id: str) -> str:
    """
    Get the appropriate MongoDB collection name for a given survey_id.

    Args:
        survey_id: The survey ID to look up

    Returns:
        The collection name to use for this survey
    """
    # Reverse the mapping to get collection name from survey_id
    return SURVEY_COLLECTIONS.get(survey_id, DEFAULT_COLLECTION)

# Function to create an insight generator for a specific survey
def create_insight_generator(survey_id: str) -> SurveyInsightGenerator:
    """
    Create a SurveyInsightGenerator instance for a specific survey.

    Args:
        survey_id: The survey ID to create a generator for

    Returns:
        A configured SurveyInsightGenerator instance
    """
    collection_name = get_collection_for_survey(survey_id)
    # try:
    #     return SurveyInsightGenerator(
    #         mongo_uri=MONGO_URI,
    #         db_name=DB_NAME,
    #         collection_name=collection_name,
    #         openai_api_key=OPENAI_API_KEY,
    #         llm_provider=None,  # Will be set per request
    #         model_name=None     # Will be set per request
    #     )
    # except Exception as e:
    #     print(f"Warning: Failed to initialize SurveyInsightGenerator for {survey_id} (collection: {collection_name}): {str(e)}")
    #     return None

@router.post("/generate",
             response_model=InsightResponse,
             status_code=status.HTTP_200_OK,
             summary="Generate insights from survey responses",
             description="Analyze survey responses and generate insights based on user query",
             response_description="Returns generated insights with metadata")
async def generate_insight(
    request: InsightRequest = Body(..., examples=[examples["insight_generator"]["value"]])
):
    """
    Generate insights from survey responses based on a user query.

    This endpoint:
    1. Retrieves survey responses from MongoDB
    2. Filters responses based on criteria extracted from the query
    3. Uses OpenAI to generate insights from the filtered responses

    Example request:
    ```json
    {
        "survey_id": "survey_341",
        "user_query": "Summarize how users aged 18-25 in New York responded to questions about ease of use.",
        "llm_provider": "OpenAI",
        "model_name": "gpt-4o",
        "model_params": {
            "temperature": 0.3,
            "max_tokens": 1500
        }
    }
    ```

    Example response:
    ```json
    {
        "survey_id": "survey_341",
        "query": "Summarize how users aged 18-25 in New York responded to questions about ease of use.",
        "insight": "Users aged 18-25 in New York generally found the product easy to use, with 85% reporting positive experiences. Many specifically mentioned the intuitive interface and quick learning curve. However, some users in this demographic suggested improvements to the mobile experience.",
        "metadata": {
            "filtered_count": 15,
            "total_count": 120,
            "filters_applied": {
                "age_min": 18,
                "age_max": 25,
                "region": "New York"
            },
            "model": "gpt-4o",
            "temperature": 0.3,
            "max_tokens": 1500,
            "generated_at": "2025-05-22T01:23:45.678Z"
        }
    }
    ```
    """
    # Create an insight generator for this specific survey
    insight_generator = create_insight_generator(request.survey_id)

    if not insight_generator:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Insight generator is not available for survey_id: {request.survey_id}. Check server logs for details."
        )

    try:
        # Generate insights
        result = insight_generator.generate_insight(
            survey_id=request.survey_id,
            user_query=request.user_query,
            llm_config={
                "provider": request.llm_provider,
                "model": request.model_name,
                "temperature": request.model_params.get("temperature", 0.3),
                "max_tokens": request.model_params.get("max_tokens", 2000)
            }
        )

        return result

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate insights: {str(e)}"
        )

@router.get("/surveys/{survey_id}/summary",
            response_model=Dict[str, Any],
            summary="Get survey summary",
            description="Get a summary of demographic information and response patterns for a survey",
            response_description="Returns demographic summary of survey responses")
async def get_survey_summary(survey_id: str):
    """
    Get a summary of responses for a specific survey.

    This endpoint:
    1. Retrieves all responses for the specified survey
    2. Generates a summary of demographic information and response patterns

    Example request:
    ```
    GET /insights/surveys/survey_341/summary
    ```

    Example response:
    ```json
    {
        "survey_id": "survey_341",
        "total_responses": 120,
        "demographics": {
            "age_groups": {
                "18-25": 45,
                "26-35": 38,
                "36-45": 22,
                "46+": 15
            },
            "regions": {
                "New York": 35,
                "California": 28,
                "Texas": 15,
                "Other": 42
            }
        },
        "generated_at": "2025-05-22T01:23:45.678Z"
    }
    ```
    """
    # Create an insight generator for this specific survey
    insight_generator = create_insight_generator(survey_id)

    if not insight_generator:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Insight generator is not available for survey_id: {survey_id}. Check server logs for details."
        )

    try:
        # Get all responses for the survey
        responses = insight_generator.get_survey_responses(survey_id)

        if not responses:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"No responses found for survey_id: {survey_id}"
            )

        # Process demographic information
        age_groups = {
            "18-25": 0,
            "26-35": 0,
            "36-45": 0,
            "46+": 0,
            "Unknown": 0
        }

        regions = {}

        for response in responses:
            # Process age
            age = response.get("age")
            if age is None:
                age_groups["Unknown"] += 1
            elif age <= 25:
                age_groups["18-25"] += 1
            elif age <= 35:
                age_groups["26-35"] += 1
            elif age <= 45:
                age_groups["36-45"] += 1
            else:
                age_groups["46+"] += 1

            # Process region
            region = response.get("region", "Unknown")
            if region:
                regions[region] = regions.get(region, 0) + 1
            else:
                regions["Unknown"] = regions.get("Unknown", 0) + 1

        # Sort regions by count (descending)
        sorted_regions = dict(sorted(regions.items(), key=lambda x: x[1], reverse=True))

        # Limit to top 5 regions + "Other"
        if len(sorted_regions) > 5:
            top_regions = dict(list(sorted_regions.items())[:5])
            other_count = sum(list(sorted_regions.values())[5:])
            top_regions["Other"] = other_count
            sorted_regions = top_regions

        return {
            "survey_id": survey_id,
            "total_responses": len(responses),
            "demographics": {
                "age_groups": age_groups,
                "regions": sorted_regions
            },
            "generated_at": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate survey summary: {str(e)}"
        )
