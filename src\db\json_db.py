"""
JSON Database for storing survey responses.
This is a simple file-based database that stores data in JSON format.
"""

import json
import os
import time
from typing import List, Dict, Any, Optional
from datetime import datetime
import uuid

# Define the path to the JSON database file
DB_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "data")
RESPONSES_FILE = os.path.join(DB_DIR, "survey_responses.json")

# Ensure the data directory exists
os.makedirs(DB_DIR, exist_ok=True)

class JSONDatabase:
    """Simple JSON file-based database for storing survey responses."""
    
    def __init__(self, file_path: str = RESPONSES_FILE):
        """Initialize the database with the specified file path."""
        self.file_path = file_path
        self._ensure_file_exists()
    
    def _ensure_file_exists(self) -> None:
        """Ensure the database file exists, creating it if necessary."""
        if not os.path.exists(self.file_path):
            # Create an empty database structure
            with open(self.file_path, 'w') as f:
                json.dump({
                    "responses": [],
                    "last_updated": datetime.now().isoformat()
                }, f, indent=2)
    
    def _read_data(self) -> Dict[str, Any]:
        """Read the current data from the database file."""
        try:
            with open(self.file_path, 'r') as f:
                return json.load(f)
        except json.JSONDecodeError:
            # If the file is corrupted, create a new empty database
            return {
                "responses": [],
                "last_updated": datetime.now().isoformat()
            }
    
    def _write_data(self, data: Dict[str, Any]) -> None:
        """Write data to the database file."""
        # Update the last_updated timestamp
        data["last_updated"] = datetime.now().isoformat()
        
        with open(self.file_path, 'w') as f:
            json.dump(data, f, indent=2)
    
    def add_response(self, response: Dict[str, Any]) -> str:
        """
        Add a new survey response to the database.
        
        Args:
            response: The survey response to add
            
        Returns:
            The ID of the newly added response
        """
        # Read the current data
        data = self._read_data()
        
        # Ensure the response has an ID
        if "id" not in response:
            response["id"] = str(uuid.uuid4())
        
        # Ensure the response has a created_at timestamp
        if "created_at" not in response:
            response["created_at"] = datetime.now().isoformat()
        
        # Add the response to the list
        data["responses"].append(response)
        
        # Write the updated data back to the file
        self._write_data(data)
        
        return response["id"]
    
    def get_response(self, response_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a survey response by ID.
        
        Args:
            response_id: The ID of the response to retrieve
            
        Returns:
            The response if found, None otherwise
        """
        # Read the current data
        data = self._read_data()
        
        # Find the response with the specified ID
        for response in data["responses"]:
            if response.get("id") == response_id:
                return response
        
        return None
    
    def get_responses_by_survey(self, survey_id: str) -> List[Dict[str, Any]]:
        """
        Get all responses for a specific survey.
        
        Args:
            survey_id: The ID of the survey
            
        Returns:
            A list of responses for the specified survey
        """
        # Read the current data
        data = self._read_data()
        
        # Filter responses by survey_id
        return [r for r in data["responses"] if r.get("survey_id") == survey_id]
    
    def get_responses_by_user(self, user_id: str) -> List[Dict[str, Any]]:
        """
        Get all responses from a specific user.
        
        Args:
            user_id: The ID of the user
            
        Returns:
            A list of responses from the specified user
        """
        # Read the current data
        data = self._read_data()
        
        # Filter responses by user_id
        return [r for r in data["responses"] if r.get("user_id") == user_id]
    
    def get_all_responses(self) -> List[Dict[str, Any]]:
        """
        Get all survey responses.
        
        Returns:
            A list of all responses in the database
        """
        # Read the current data
        data = self._read_data()
        
        return data["responses"]
    
    def update_response(self, response_id: str, updates: Dict[str, Any]) -> bool:
        """
        Update a survey response.
        
        Args:
            response_id: The ID of the response to update
            updates: The fields to update
            
        Returns:
            True if the response was updated, False otherwise
        """
        # Read the current data
        data = self._read_data()
        
        # Find the response with the specified ID
        for i, response in enumerate(data["responses"]):
            if response.get("id") == response_id:
                # Update the response
                data["responses"][i].update(updates)
                
                # Write the updated data back to the file
                self._write_data(data)
                
                return True
        
        return False
    
    def delete_response(self, response_id: str) -> bool:
        """
        Delete a survey response.
        
        Args:
            response_id: The ID of the response to delete
            
        Returns:
            True if the response was deleted, False otherwise
        """
        # Read the current data
        data = self._read_data()
        
        # Find the response with the specified ID
        for i, response in enumerate(data["responses"]):
            if response.get("id") == response_id:
                # Remove the response
                del data["responses"][i]
                
                # Write the updated data back to the file
                self._write_data(data)
                
                return True
        
        return False

# Create a singleton instance of the database
db = JSONDatabase()
