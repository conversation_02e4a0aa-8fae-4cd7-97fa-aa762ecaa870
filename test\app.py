import streamlit as st
import os
import uuid
import time
import openai
import json
from datetime import datetime
from typing import List, Dict, Any, Optional
from openai import OpenAI
import pandas as pd
# from dotenv import load_dotenv
# from survey_insight_generator import SurveyInsightGenerator
from app.api.survey_insight_generator import SurveyInsightGenerator

# Load environment variables
# load_dotenv()

# Configure OpenAI
api_key = os.environ.get("OPENAI_API_KEY")
if not api_key:
    raise ValueError("No OpenAI API key found. Please set the OPENAI_API_KEY environment variable.")

# Initialize OpenAI client
openai.api_key = api_key

# Set page configuration
st.set_page_config(
    page_title="Survey Insight System",
    page_icon="📊",
    layout="wide"
)

# List of common languages
LANGUAGES = [
    "English", "Spanish", "French", "German", "Italian", "Portuguese", "Dutch",
    "Russian", "Chinese", "Japanese", "Korean", "Arabic", "Hindi", "Bengali",
    "Urdu", "Turkish", "Persian", "Thai", "Vietnamese", "Indonesian", "Malay",
    "Swahili", "Polish", "Ukrainian", "Czech", "Slovak", "Hungarian", "Romanian",
    "Bulgarian", "Greek", "Swedish", "Norwegian", "Danish", "Finnish", "Hebrew"
]

# Initialize session state
if "sessions" not in st.session_state:
    st.session_state.sessions = {}
if "current_session_id" not in st.session_state:
    st.session_state.current_session_id = None
if "questions" not in st.session_state:
    st.session_state.questions = []
if "display_questions" not in st.session_state:
    st.session_state.display_questions = []
if "chat_history" not in st.session_state:
    st.session_state.chat_history = []

# Function to generate questions using OpenAI
def generate_questions_from_llm(
    context: str,
    language: str,
    num_questions: int = 5,
    messages: List[Dict[str, str]] = None
) -> List[str]:
    """Generate survey questions using OpenAI."""

    # Create system prompt
    system_prompt = f"""
You are a survey question generator. Generate EXACTLY {num_questions} brief and relevant survey questions in {language} based on the given context.
Return ONLY the questions as a JSON array of strings with EXACTLY {num_questions} questions. Do not include any explanations or additional text.
Example response format: ["Question 1?", "Question 2?", "Question 3?"]

IMPORTANT: You must generate EXACTLY {num_questions} questions, no more and no less.
"""

    # Create messages for the API call
    if messages is None:
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": f"Context: {context}"}
        ]
    else:
        # Add the new context to existing messages
        messages.append({"role": "user", "content": f"Additional context: {context}"})

    # Make the API call
    openai_client = openai.OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
    response = openai_client.chat.completions.create(
        model="gpt-4",  # Using GPT-4 as specified
        messages=messages,
        temperature=0.7,
        max_tokens=1000
    )

    # Extract and parse the response
    try:
        content = response.choices[0].message.content

        # Try to parse as JSON
        try:
            questions_data = json.loads(content)

            # Check if the response has a 'questions' field or is directly an array
            if isinstance(questions_data, dict) and "questions" in questions_data:
                questions = questions_data["questions"]
            elif isinstance(questions_data, list):
                questions = questions_data
            else:
                # Try to find any array in the response
                questions = None
                for key, value in questions_data.items():
                    if isinstance(value, list):
                        questions = value
                        break

                # If no array found, create a list with the content as a single question
                if questions is None:
                    questions = [content]
        except json.JSONDecodeError:
            # If not valid JSON, split by newlines and return as list
            lines = [line.strip() for line in content.split('\n') if line.strip()]
            if lines:
                questions = lines
            else:
                questions = [content]

        # Ensure we return exactly num_questions questions
        if len(questions) > num_questions:
            questions = questions[:num_questions]  # Truncate to the requested number
        elif len(questions) < num_questions:
            # If we have fewer questions than requested, add generic ones to reach the target
            for i in range(len(questions), num_questions):
                questions.append(f"Additional question {i+1}?")

        # Double-check we have exactly the right number
        return questions[:num_questions]
    except Exception as e:
        st.error(f"Error parsing LLM response: {str(e)}")
        try:
            st.write("Raw response:", response.choices[0].message.content)
            # Return the raw content as a single question, and pad with generic questions if needed
            error_questions = [response.choices[0].message.content]

            # If we need more questions to meet the requested number
            while len(error_questions) < num_questions:
                error_questions.append(f"Additional question {len(error_questions) + 1}?")

            return error_questions[:num_questions]  # Ensure we return exactly num_questions
        except:
            # Create generic questions if we can't get the raw response
            return [f"Question {i+1}?" for i in range(num_questions)]

# Function to extract number of questions from context using regex
def extract_num_questions_regex(context: str) -> int:
    """Extract number of questions using regex pattern matching."""
    import re

    # Common patterns for specifying question counts
    patterns = [
        # Explicit number followed by "questions" or similar
        r"(\d+)\s+(?:questions|survey questions|items)",
        # Number words followed by "questions" or similar
        r"(one|two|three|four|five|six|seven|eight|nine|ten)\s+(?:questions|survey questions|items)",
        # "Generate X questions" pattern
        r"generate\s+(\d+)",
        # "X-question survey" pattern
        r"(\d+)[\s-]question",
        # "Need X questions" pattern
        r"need\s+(\d+)",
        # "Create X questions" pattern
        r"create\s+(\d+)",
        # "Give X more questions" pattern
        r"give\s+(one|two|three|four|five|six|seven|eight|nine|ten|\d+)(?:\s+more)?\s+questions",
        # "X more questions" pattern
        r"(one|two|three|four|five|six|seven|eight|nine|ten|\d+)\s+more\s+questions"
    ]

    # Word to number mapping
    word_to_num = {
        "one": 1, "two": 2, "three": 3, "four": 4, "five": 5,
        "six": 6, "seven": 7, "eight": 8, "nine": 9, "ten": 10
    }

    # Convert context to lowercase for case-insensitive matching
    context_lower = context.lower()

    # Try each pattern
    for pattern in patterns:
        match = re.search(pattern, context_lower)
        if match:
            num_str = match.group(1)
            try:
                # Try to convert to integer
                return int(num_str)
            except ValueError:
                # If it's a word, convert to number
                if num_str in word_to_num:
                    return word_to_num[num_str]

    # Default to 5 if no pattern matches
    return 5

# Function to extract number of questions from context
def extract_num_questions(context: str) -> int:
    """Extract the number of questions to generate from the context.

    First tries to use the LLM to intelligently determine the number,
    then falls back to regex pattern matching if that fails.
    """
    try:
        openai_client = openai.OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        # Use OpenAI to extract the number
        response = openai_client.chat.completions.create(
            model="gpt-4",  # Using GPT-4 as specified
            messages=[
                {"role": "system", "content": """
Analyze the following survey context and determine how many questions should be generated.
If a specific number is mentioned (like "5 questions", "ten survey questions", "give me 3 questions", "two more questions"), extract that number.
If a range is given, use the average of that range.
If no number is specified, return 5.
Return ONLY a number as a digit, nothing else. Just the number, no text.
"""},
                {"role": "user", "content": context}
            ],
            temperature=0,
            max_tokens=10
        )

        # Extract the number from the response
        num_str = response.choices[0].message.content.strip()

        # Try to convert to integer
        try:
            num = int(num_str)
            # Limit to a reasonable range
            return max(1, min(num, 20))
        except ValueError:
            # If LLM fails to return a valid number, fall back to regex
            return extract_num_questions_regex(context)
    except Exception as e:
        # Log the error
        print(f"Error in extract_num_questions: {str(e)}")
        # If OpenAI call fails, fall back to regex
        return extract_num_questions_regex(context)

# Session management functions
def create_session(context: str, language: str) -> str:
    """Create a new session."""
    session_id = f"session-{uuid.uuid4().hex[:8]}"
    st.session_state.sessions[session_id] = {
        "created_at": time.time(),
        "last_accessed": time.time(),
        "language": language,
        "context": context,
        "messages": [
            {"role": "system", "content": "You generate relevant survey questions in JSON."},
            {"role": "user", "content": f"Context: {context}"}
        ],
        "questions": []  # Initialize with empty array
    }

    # Clear any existing questions in the session state
    st.session_state.questions = []
    st.session_state.display_questions = []

    return session_id

def get_session(session_id: str) -> Optional[Dict[str, Any]]:
    """Get a session by ID."""
    session = st.session_state.sessions.get(session_id)
    if not session:
        return None

    # Update last accessed time
    session["last_accessed"] = time.time()
    return session

def update_session(session_id: str, new_context: str, questions: List[str]) -> None:
    """Update a session with new context and questions."""
    session = get_session(session_id)
    if not session:
        return

    # Update context
    if new_context:
        session["context"] += f"\n\nAdditional context: {new_context}"
        session["messages"].append({"role": "user", "content": f"Additional context: {new_context}"})

    # Ensure questions is a list of strings
    question_strings = []
    for q in questions:
        if isinstance(q, str):
            question_strings.append(q)
        elif isinstance(q, dict) and "text" in q:
            question_strings.append(q["text"])
        elif isinstance(q, dict) and "question" in q:
            question_strings.append(q["question"])
        else:
            # If we can't extract a string, convert the whole item to a string
            question_strings.append(str(q))

    # Add assistant response
    if question_strings:
        session["messages"].append({
            "role": "assistant",
            "content": f"Generated questions: {', '.join(question_strings)}"
        })
    else:
        session["messages"].append({
            "role": "assistant",
            "content": "No questions were generated."
        })

    # Store only the current questions instead of appending
    session["questions"] = question_strings

# Function to clear displayed questions
def clear_displayed_questions():
    """Clear the questions displayed in the UI."""
    st.session_state.display_questions = []
    st.session_state.questions = []

# Function to save responses to a local file
def save_responses_to_file(responses, file_path="data/survey_responses.json"):
    """Save survey responses to a local JSON file."""
    try:
        # Create data directory if it doesn't exist
        os.makedirs(os.path.dirname(file_path), exist_ok=True)

        # Save to a file
        with open(file_path, "w") as f:
            json.dump({"responses": responses}, f, indent=2)

        return True, f"Responses saved to {file_path}"
    except Exception as e:
        return False, f"Error saving responses: {str(e)}"

# Function to load responses from a local file
def load_responses_from_file(file_path="data/survey_responses.json"):
    """Load survey responses from a local JSON file."""
    try:
        # Check if the file exists
        if os.path.exists(file_path):
            # Load from file
            with open(file_path, "r") as f:
                loaded_data = json.load(f)

            # Return the responses
            if "responses" in loaded_data:
                return True, loaded_data["responses"]
            else:
                return False, "No responses found in the file"
        else:
            return False, f"File not found: {file_path}"
    except Exception as e:
        return False, f"Error loading responses: {str(e)}"

# Helper functions for query analysis
def is_generic_or_irrelevant_query(query, insight_text=None):
    """Check if query is too generic - VERY CONSERVATIVE (insight_text kept for compatibility)"""
    query_lower = query.lower().strip()

    # Only flag extremely generic test queries - be very conservative
    extremely_generic_patterns = [
        "test", "testing", "this is a test", "hello", "hi", "check", "sample",
        "example", "demo", "trial"
    ]

    # Check if query is extremely short (less than 3 characters)
    if len(query.strip()) < 3:
        return True

    # Only flag if query is exactly one of the test patterns
    if query_lower in extremely_generic_patterns:
        return True

    # Only flag if query is very short AND contains only generic test words
    query_words = query_lower.split()
    if len(query_words) == 1 and query_words[0] in extremely_generic_patterns:
        return True

    # Don't check AI response indicators anymore - let the LLM handle this
    # The LLM is now smart enough to make these decisions

    return False

def get_example_queries(survey_id):
    """Get example queries based on survey type"""
    examples = {
        "survey_341": [  # Teaching Quality
            "How satisfied are students with teacher communication?",
            "What are the main concerns about curriculum effectiveness?",
            "How do teachers rate professional development opportunities?",
            "What feedback do students give about classroom technology?",
            "How effective are current student engagement strategies?"
        ],
        "survey_342": [  # Electric Vehicles
            "What are users' main concerns about electric vehicle charging?",
            "How satisfied are EV owners with battery performance?",
            "What factors influence electric vehicle purchase decisions?",
            "How do users rate the availability of charging infrastructure?",
            "What are the biggest barriers to EV adoption?"
        ],
        "survey_343": [  # AI Tools
            "How do users rate AI tool productivity benefits?",
            "What are the main concerns about AI tool reliability?",
            "How satisfied are users with AI-generated content quality?",
            "What challenges do users face when adopting AI tools?",
            "How do different age groups perceive AI tool usefulness?"
        ],
        "survey_344": [  # Online Payments
            "How secure do users feel when making online payments?",
            "What are the most preferred online payment methods?",
            "What concerns do users have about digital payment security?",
            "How satisfied are users with mobile payment experiences?",
            "What improvements do users want in payment platforms?"
        ],
        "survey_345": [  # Remote Work
            "How has remote work affected productivity levels?",
            "What are the biggest challenges of working from home?",
            "How satisfied are employees with remote work tools?",
            "What impact has remote work had on work-life balance?",
            "How do managers evaluate remote team performance?"
        ]
    }

    return examples.get(survey_id, [
        "How satisfied are users with the service quality?",
        "What are the main areas for improvement?",
        "How do different demographics rate their experience?",
        "What are users' biggest concerns or challenges?",
        "How effective are current solutions or features?"
    ])

def get_collection_for_survey(survey_id):
    """Map survey ID to collection name"""
    survey_collections = {
        "survey_342": "ev_survey_2025",
        "survey_345": "remore_work",
        "survey_343": "AI_tools",
        "survey_341": "teaching_quality",
        "survey_344": "online_payments"
    }
    return survey_collections.get(survey_id, "teaching_quality")

# Main app layout
st.title("Survey Insight System")

# Sidebar
with st.sidebar:
    st.header("About")
    st.markdown("""
    This is a standalone version of the Survey Insight System using GPT-4.1.

    It allows you to generate survey questions based on context and language, with session management for continued conversations.
    """)

    st.header("Environment")
    if api_key:
        st.success("OpenAI API key is configured")
    else:
        st.error("OpenAI API key is missing")

    st.header("Model")
    st.info("Using GPT-4.1 for question generation")

# Create a radio button to select tabs (more reliable than st.tabs for tracking changes)
if "active_tab" not in st.session_state:
    st.session_state.active_tab = "Question Generator-UI"
    clear_displayed_questions()

# Use a radio button to select tabs
selected_tab = st.radio("Select Tab:", ["Question Generator-UI", "Question Generator-API"], horizontal=True, label_visibility="collapsed")

# Check if tab has changed
if selected_tab != st.session_state.active_tab:
    st.session_state.active_tab = selected_tab
    clear_displayed_questions()

# Create a container for each tab
tab1_container = st.container()  # Question Generator-UI
tab2_container = st.container()  # Question Generator-API

# Tab 1: Question Generator-UI
if selected_tab == "Question Generator-UI":
    with tab1_container:
        st.header("Survey Insight System")

        # Create a radio button for UI subtabs
        if "active_ui_tab" not in st.session_state:
            st.session_state.active_ui_tab = "Question Generator"
            clear_displayed_questions()

        # Use a radio button to select UI tabs
        ui_selected_tab = st.radio(
            "Select Option:",
            ["Question Generator", "Response Generator", "View Stored Responses", "Survey Insights"],
            horizontal=True,
            key="ui_tab_selector",
            index=3
        )

        # Check if UI tab has changed
        if ui_selected_tab != st.session_state.active_ui_tab:
            st.session_state.active_ui_tab = ui_selected_tab
            clear_displayed_questions()

        # Create containers for each UI tab
        ui_tab1_container = st.container()  # Question Generator
        ui_tab2_container = st.container()  # Response Generator
        ui_tab3_container = st.container()  # View Stored Responses
        ui_tab4_container = st.container()  # Survey Insights

        # Question Generator tab
        if ui_selected_tab == "Question Generator":
            with ui_tab1_container:
                st.subheader("Generate Survey Questions")

                # Input form
                with st.form("question_generator_form"):
                    # Context input
                    context = st.text_area(
                        "Context for Survey Questions",
                        height=200,
                        placeholder="Enter the context for generating survey questions..."
                    )

                    # Language selection
                    language = st.selectbox("Language", LANGUAGES)

                    # Chat thread toggle
                    use_same_thread = st.toggle("Continue same chat thread", value=False)

                    # Session ID input (optional)
                    if use_same_thread and st.session_state.current_session_id:
                        st.info(f"Using session: {st.session_state.current_session_id}")
                    elif use_same_thread:
                        session_id = st.text_input(
                            "Session ID (Optional)",
                            placeholder="Enter a session ID to continue a thread..."
                        )
                        if session_id:
                            st.session_state.current_session_id = session_id

                    # Submit button
                    submit_button = st.form_submit_button("Generate Questions")

                # Process form submission
                if submit_button and context and language:
                    # Extract number of questions before showing spinner
                    num_questions = extract_num_questions(context)
                    st.info(f"Will generate exactly {num_questions} questions based on your context")

                    with st.spinner("Generating questions..."):
                        # Determine if we should use an existing session
                        current_session_id = st.session_state.current_session_id if use_same_thread else None
                        is_continued_session = False
                        messages = None

                        # Check if this is a continued session
                        if current_session_id:
                            session = get_session(current_session_id)
                            if session:
                                language = language or session["language"]
                                messages = session["messages"]
                                is_continued_session = True

                        # Generate questions
                        questions = generate_questions_from_llm(
                            context=context,
                            language=language,
                            num_questions=num_questions,
                            messages=messages
                        )

                        # Create or update session
                        if is_continued_session:
                            # Update existing session
                            update_session(current_session_id, context, questions)
                        else:
                            # Create new session
                            current_session_id = create_session(context, language)
                            update_session(current_session_id, "", questions)

                        # Update session state - only store current session ID
                        st.session_state.current_session_id = current_session_id

                        # Clear any previous questions and store only the newly generated questions for display
                        st.session_state.questions = []  # Clear existing questions
                        st.session_state.display_questions = questions.copy()  # Use a copy to avoid reference issues

                        # Add to chat history (keep this for internal tracking)
                        if "chat_history" not in st.session_state:
                            st.session_state.chat_history = []
                        st.session_state.chat_history.append({
                            "context": context,
                            "language": language,
                            "is_continued": is_continued_session
                        })

                        # Display success message
                        st.success(f"Generated {num_questions} questions as requested!")

                        # Display session info
                        st.info(f"Session ID: {current_session_id}")
                        st.info(f"Continued session: {is_continued_session}")
                        st.info(f"Model: GPT-4.1")

                # Display generated questions - use display_questions to ensure only new questions are shown
                if st.session_state.display_questions:
                    st.subheader("Generated Questions")
                    for i, question in enumerate(st.session_state.display_questions, 1):
                        # Ensure question is a string
                        if isinstance(question, str):
                            st.write(f"{i}. {question}")
                        elif isinstance(question, dict) and "text" in question:
                            st.write(f"{i}. {question['text']}")
                        elif isinstance(question, dict) and "question" in question:
                            st.write(f"{i}. {question['question']}")
                        else:
                            st.write(f"{i}. {str(question)}")

                    # Display as JSON
                    with st.expander("View as JSON", expanded=False):
                        response = {
                            "session_id": st.session_state.current_session_id,
                            "questions": st.session_state.display_questions,
                            "is_continued_session": is_continued_session if 'is_continued_session' in locals() else False,
                            "metadata": {
                                "language": language if 'language' in locals() else "",
                                "num_questions": len(st.session_state.display_questions),
                                "model": "gpt-4.1"
                            }
                        }
                        st.json(response)

        # Response Generator tab
        elif ui_selected_tab == "Response Generator":
            with ui_tab2_container:
                st.subheader("Submit Survey Responses")

                # Initialize session state for responses
                if "survey_responses" not in st.session_state:
                    st.session_state.survey_responses = []

                # Initialize session state for the current response
                if "current_response" not in st.session_state:
                    st.session_state.current_response = {
                        "survey_id": "",
                        "user_id": "",
                        "age": None,
                        "region": "",
                        "answers": []
                    }

                # Survey ID input
                survey_id = st.text_input(
                    "Survey ID",
                    value=st.session_state.current_response.get("survey_id", ""),
                    help="Enter the ID of the survey (e.g., session-12345678)"
                )

                # User ID input
                user_id = st.text_input(
                    "User ID",
                    value=st.session_state.current_response.get("user_id", ""),
                    help="Enter a unique identifier for the respondent"
                )

                # Demographic information
                col1, col2 = st.columns(2)
                with col1:
                    age = st.number_input(
                        "Age",
                        min_value=0,
                        max_value=120,
                        value=st.session_state.current_response.get("age", 0) or 0,
                        help="Enter the respondent's age (optional)"
                    )

                with col2:
                    region = st.text_input(
                        "Region",
                        value=st.session_state.current_response.get("region", ""),
                        help="Enter the respondent's geographic region (optional)"
                    )

                # Questions and answers
                st.subheader("Questions and Answers")

                # Use the current session's questions if available
                questions = []
                if st.session_state.current_session_id:
                    session = get_session(st.session_state.current_session_id)
                    if session:
                        questions = session.get("questions", [])

                if questions:
                    st.info(f"Using questions from session: {st.session_state.current_session_id}")

                    # Create a form for the answers
                    with st.form("survey_response_form"):
                        answers = []

                        for i, question in enumerate(questions):
                            answer = st.text_area(
                                f"Q{i+1}: {question}",
                                height=100,
                                key=f"answer_{i}"
                            )

                            if answer:
                                answers.append({
                                    "question": question,
                                    "answer": answer
                                })

                        # Submit button
                        submit_button = st.form_submit_button("Submit Responses")

                        if submit_button:
                            if not survey_id:
                                st.error("Survey ID is required")
                            elif not user_id:
                                st.error("User ID is required")
                            elif not answers:
                                st.error("At least one answer is required")
                            else:
                                # Create the response object
                                response = {
                                    "survey_id": survey_id,
                                    "user_id": user_id,
                                    "age": age if age > 0 else None,
                                    "region": region if region else None,
                                    "answers": answers
                                }

                                # Store the response
                                st.session_state.survey_responses.append(response)

                                # Update the current response
                                st.session_state.current_response = {
                                    "survey_id": survey_id,
                                    "user_id": user_id,
                                    "age": age if age > 0 else None,
                                    "region": region if region else None,
                                    "answers": []
                                }

                                # Show success message
                                st.success("Survey responses submitted successfully!")

                                # Show the response ID
                                response_id = f"response-{uuid.uuid4().hex[:8]}"
                                st.info(f"Response ID: {response_id}")

                                # Store the response with the ID
                                response["id"] = response_id
                                response["created_at"] = time.time()

                                # Auto-save responses to file
                                success, message = save_responses_to_file(st.session_state.survey_responses)
                                if success:
                                    st.info(message)

                                # Display the response as JSON
                                st.json(response)
                else:
                    st.warning("No questions available. Please generate questions first in the Question Generator tab.")

                    # Add a button to go to Question Generator
                    if st.button("Go to Question Generator"):
                        st.session_state.active_ui_tab = "Question Generator"
                        st.experimental_rerun()

        # View Stored Responses tab
        elif ui_selected_tab == "View Stored Responses":
            with ui_tab3_container:
                st.subheader("View Stored Responses")

                # Add search functionality
                st.subheader("Search Responses")
                search_col1, search_col2 = st.columns(2)

                with search_col1:
                    search_survey_id = st.text_input("Search by Survey ID", placeholder="Enter survey ID...", key="ui_search_survey_id")

                with search_col2:
                    search_user_id = st.text_input("Search by User ID", placeholder="Enter user ID...", key="ui_search_user_id")

                # Add a button to load responses from local file
                if st.button("Load Responses from Local File", key="ui_load_responses"):
                    success, result = load_responses_from_file()
                    if success:
                        # Initialize survey_responses if not already done
                        if "survey_responses" not in st.session_state:
                            st.session_state.survey_responses = []

                        # Add the loaded responses to the list
                        st.session_state.survey_responses.extend(result)

                        st.success(f"Loaded {len(result)} responses from file")
                    else:
                        st.warning(result)

                # Filter responses based on search criteria
                if "survey_responses" in st.session_state and st.session_state.survey_responses:
                    filtered_responses = st.session_state.survey_responses

                    if search_survey_id:
                        filtered_responses = [r for r in filtered_responses if search_survey_id.lower() in r.get('survey_id', '').lower()]

                    if search_user_id:
                        filtered_responses = [r for r in filtered_responses if search_user_id.lower() in r.get('user_id', '').lower()]

                    # Display the number of results
                    # st.write(f"Found {len(filtered_responses)} responses")

                    # Create a JSON response object
                    response_obj = {
                        "count": len(filtered_responses),
                        "responses": filtered_responses
                    }

                    # Display as JSON
                    st.json(response_obj)

                    # Add a button to download all responses as JSON
                    if st.button("Download All Responses", key="ui_download_all"):
                        # Create a JSON string with all responses
                        all_json_str = json.dumps(response_obj, indent=2)
                        # Create a download button
                        st.download_button(
                            label="Download All Responses as JSON",
                            data=all_json_str,
                            file_name="all_responses.json",
                            mime="application/json",
                            key="ui_download_all_button"
                        )

                    # Add a button to save responses to a local file
                    if st.button("Save Responses to Local File", key="ui_save_responses"):
                        success, message = save_responses_to_file(filtered_responses)
                        if success:
                            st.success(message)
                        else:
                            st.error(message)
                else:
                    st.info("No responses found. Submit responses first or load from a local file.")

        # Survey Insights tab
        elif ui_selected_tab == "Survey Insights":
            with ui_tab4_container:
                st.subheader("Generate Survey Insights")
                st.markdown("""
                This tool uses AI to analyze survey responses and generate insights based on your queries.
                You can ask questions about specific demographics, regions, or topics to get targeted insights.
                """)

                st.markdown("**Available Survey IDs and Topics:**")
                survey_topics = {
                    "survey_341": "Teaching Quality",
                    "survey_342": "Electric Vehicle (EV) Usage",
                    "survey_343": "Use of AI Assistants and Applications",
                    "survey_344": "Digital Payment Platforms",
                    "survey_345": "Remote Work & Hybrid Productivity"
                }
                # Display survey topics in two columns
                col1, col2 = st.columns(2)
                topics_list = list(survey_topics.items())
                mid = (len(topics_list) + 1) // 2
                for idx, (sid, topic) in enumerate(topics_list):
                    if idx < mid:
                        col1.markdown(f"- `{sid}`: {topic}")
                    else:
                        col2.markdown(f"- `{sid}`: {topic}")

                # Survey ID to collection name mapping
                SURVEY_COLLECTIONS = {
                    "survey_342": "ev_survey_2025",
                    "survey_345": "remore_work",
                    "survey_343": "AI_tools",
                    "survey_341": "teaching_quality",
                    "survey_344": "online_payments"
                }

                # Default collection name if survey_id is not in the mapping
                DEFAULT_COLLECTION = "teaching_quality"

                # Initialize MongoDB connection parameters
                MONGO_URI = os.environ.get("MONGODB_URI")
                DB_NAME = os.environ.get("MONGODB_DB_NAME", "survey_dev_db")
                OPENAI_API_KEY = os.environ.get("OPENAI_API_KEY")

                def get_collection_for_survey(survey_id: str) -> str:
                    """
                    Get the appropriate MongoDB collection name for a given survey_id.

                    Args:
                        survey_id: The survey ID to look up

                    Returns:
                        The collection name to use for this survey
                    """
                    # Reverse the mapping to get collection name from survey_id
                    return SURVEY_COLLECTIONS.get(survey_id, DEFAULT_COLLECTION)

                col1, col2 = st.columns((0.3,0.7))
                with col1:
                    # LLM Provider Selection (using selectbox for better state management)
                    llm_provider = st.radio(
                        "Model Selection",
                        ["OpenAI", "Gemini"],
                        # index=0,
                        help="Choose the AI model provider",
                        key="ui_llm_provider_selectbox",
                        horizontal=True
                    )

                with col2:
                    with st.expander("Parameter Optimization", expanded=False):

                        # Model Selection and Parameters based on provider
                        if llm_provider == "OpenAI":

                            model_choice = st.selectbox(
                                "OpenAI Model",
                                ["gpt-4o", "gpt-4o-mini", "gpt-3.5-turbo"],
                                index=0,
                                help="Select OpenAI model variant",
                                key="ui_openai_model_choice"
                            )

                            temperature = st.slider(
                                "Temperature",
                                min_value=0.0,
                                max_value=2.0,
                                value=0.3,
                                step=0.1,
                                help="Higher values make output more random, lower values more deterministic",
                                key="ui_openai_temperature"
                            )

                            max_tokens = st.slider(
                                "Max Tokens",
                                min_value=500,
                                max_value=4000,
                                value=1500,
                                step=100,
                                help="Maximum length of the generated insight",
                                key="ui_openai_max_tokens"
                            )

                            top_p = st.slider(
                                "Top P",
                                min_value=0.0,
                                max_value=1.0,
                                value=1.0,
                                step=0.1,
                                help="Nucleus sampling parameter",
                                key="ui_openai_top_p"
                            )

                        else:  # Gemini
                            model_choice = st.selectbox(
                                "Gemini Model",
                                ["gemini-1.5-pro", "gemini-1.5-flash", "gemini-1.0-pro"],
                                index=0,
                                help="Select Gemini model variant",
                                key="ui_gemini_model_choice"
                            )

                            temperature = st.slider(
                                "Temperature",
                                min_value=0.0,
                                max_value=1.0,
                                value=0.4,
                                step=0.1,
                                help="Controls randomness in Gemini responses",
                                key="ui_gemini_temperature"
                            )

                            max_tokens = st.slider(
                                "Max Output Tokens",
                                min_value=100,
                                max_value=2048,
                                value=1024,
                                step=50,
                                help="Maximum number of tokens in Gemini response",
                                key="ui_gemini_max_tokens"
                            )

                            top_p = st.slider(
                                "Top P",
                                min_value=0.0,
                                max_value=1.0,
                                value=0.95,
                                step=0.05,
                                help="Nucleus sampling for Gemini",
                                key="ui_gemini_top_p"
                            )

                            top_k = st.slider(
                                "Top K",
                                min_value=1,
                                max_value=100,
                                value=40,
                                step=1,
                                help="Top-k sampling for Gemini",
                                key="ui_gemini_top_k"
                            )
                # Create tabs for different insight options
                insight_tabs = st.tabs(["Generate Insights", "Survey Summary", "MongoDB Connection"])

                # Tab 1: Generate Insights
                with insight_tabs[0]:
                    # Input form for generating insights
                    with st.form("insight_generator_form"):
                        # Survey ID input
                        insight_survey_id = st.text_input(
                            "Survey ID",
                            value='survey_341',
                            placeholder="Enter the survey ID to analyze (e.g., survey_341)"
                        )

                        # User query input
                        user_query = st.text_area(
                            "Your Query",
                            value='Summarize how users aged 18-25 in New York responded to questions about teaching quality.',
                            placeholder="Example: Summarize how users aged 18-25 in New York responded to questions about teaching quality.",
                            height=100
                        )

                        # # LLM Provider Selection (using selectbox for better state management)
                        # llm_provider = st.selectbox(
                        #     "LLM Provider",
                        #     ["OpenAI", "Gemini"],
                        #     index=0,
                        #     help="Choose the AI model provider",
                        #     key="ui_llm_provider_selectbox"
                        # )

                        # # Force refresh if selection changed
                        # if "last_llm_provider" not in st.session_state:
                        #     st.session_state.last_llm_provider = llm_provider
                        # elif st.session_state.last_llm_provider != llm_provider:
                        #     st.session_state.last_llm_provider = llm_provider
                        #     st.rerun()

                        # Debug info
                        # st.write(f"Debug: Selected provider = '{llm_provider}'")

                        # Advanced options


                        # Submit button
                        insight_button = st.form_submit_button("Generate Insights")

                    if insight_button:
                        if not insight_survey_id:
                            st.error("Survey ID is required")
                        elif not user_query:
                            st.error("Query is required")
                        else:
                            with st.spinner("Analyzing survey responses..."):
                                try:
                                    import time
                                    start_time = time.time()

                                    collection_name = get_collection_for_survey(insight_survey_id)
                                    generator = SurveyInsightGenerator(
                                        mongo_uri=os.environ.get("MONGO_URI"),
                                        db_name=os.environ.get("MONGODB_DB_NAME", "survey_dev_db"),
                                        collection_name=collection_name,
                                        openai_api_key=os.environ.get("OPENAI_API_KEY")
                                    )
                                    result = generator.generate_insight(insight_survey_id, user_query)

                                    end_time = time.time()
                                    execution_time = round(end_time - start_time, 2)

                                    # Handle different result scenarios
                                    insight_text = result.get("insight", "").strip()
                                    filtered_count = result.get("filtered_count", 0)
                                    total_count = result.get("total_count", 0)

                                    # Check for various failure scenarios
                                    if not insight_text:
                                        st.error(f"❌ No insight generated (took {execution_time}s)")
                                        st.warning("🔍 **No Results Found**")
                                        st.write("**Possible reasons:**")
                                        st.write("• No survey data found for the specified survey ID")
                                        st.write("• Query filters didn't match any records")
                                        st.write("• Database connection issues")

                                    elif filtered_count == 0:
                                        st.warning(f"⚠️ No matching records found (took {execution_time}s)")
                                        st.warning("🔍 **No Results Found**")
                                        st.write("**Your query didn't match any survey responses:**")
                                        st.write("• Try broadening your search criteria")
                                        st.write("• Check if the survey ID is correct")
                                        st.write("• Verify age ranges and location filters")

                                    elif insight_text.startswith("NO RELEVANT DATA:"):
                                        # Primary detection: LLM explicitly flagged the query
                                        st.warning(f"⚠️ Query issue detected by AI (took {execution_time}s)")
                                        st.warning("🔍 **Query Not Specific Enough**")
                                        st.write("**The AI detected an issue with your query:**")
                                        st.write("• Your query may be too generic or not specific enough")
                                        st.write("• Instead of 'test query', ask about specific aspects like 'teacher satisfaction' or 'student engagement'")
                                        st.write("• Try queries like: 'How do students rate teaching quality?' or 'What are the main concerns about curriculum?'")
                                        st.write("• Focus on specific demographics: 'How do users aged 25-35 feel about remote work?'")
                                        st.write("• Ask about particular topics covered in the survey")

                                        # Show example queries based on survey type
                                        with st.expander("💡 Example Queries for This Survey", expanded=True):
                                            survey_examples = get_example_queries(insight_survey_id)
                                            for example in survey_examples:
                                                st.write(f"• {example}")

                                        # Show the AI response for transparency
                                        with st.expander("📄 AI Response Details", expanded=False):
                                            st.write("**AI Response:**")
                                            st.write(insight_text)

                                    elif is_generic_or_irrelevant_query(user_query):
                                        # Fallback detection: Only for extremely generic queries the LLM might miss
                                        st.warning(f"⚠️ Query appears to be a test query (took {execution_time}s)")
                                        st.warning("🔍 **Test Query Detected**")
                                        st.write("**Your query appears to be a test or extremely generic:**")
                                        st.write("• Try asking a specific question about the survey data")
                                        st.write("• Focus on particular aspects like demographics, satisfaction, or specific topics")

                                        # Show example queries
                                        with st.expander("💡 Example Queries for This Survey", expanded=True):
                                            survey_examples = get_example_queries(insight_survey_id)
                                            for example in survey_examples:
                                                st.write(f"• {example}")

                                        # Show the AI response anyway (it might still have useful info)
                                        with st.expander("📄 AI Response Details", expanded=False):
                                            st.write("**AI Response:**")
                                            st.write(insight_text)

                                    elif "no relevant" in insight_text.lower() or "cannot find" in insight_text.lower() or "no data" in insight_text.lower() or "not available" in insight_text.lower():
                                        st.warning(f"⚠️ Query not answerable from available data (took {execution_time}s)")
                                        st.warning("🔍 **No Relevant Results Found**")
                                        st.write("**The AI couldn't find relevant information to answer your query:**")
                                        st.write("• Your question might be outside the scope of this survey")
                                        st.write("• Try rephrasing your query to match survey topics")
                                        st.write("• Consider asking about different aspects covered in the survey")

                                        # Still show the AI response for transparency
                                        with st.expander("📄 AI Response Details", expanded=False):
                                            st.write(insight_text)

                                    else:
                                        # Successful insight generation
                                        st.success(f"✅ Insight generated successfully in {execution_time} seconds")
                                        st.write("\n**Generated Insight:**")
                                        st.write("---")
                                        st.write(insight_text)

                                        # Add download buttons for QA review
                                        st.write("\n**📥 Download Raw LLM Output for QA Review:**")
                                        col1, col2 = st.columns(2)

                                        with col1:
                                            # Prepare Markdown content
                                            markdown_content = f"""# Survey Insight Analysis Report

## Query Information
- **Survey ID:** {insight_survey_id}
- **Query:** {user_query}
- **Generated At:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **Execution Time:** {execution_time} seconds

## LLM Configuration
- **Provider:** {llm_provider}
- **Model:** {model_choice}
- **Records Analyzed:** {filtered_count}
- **Total Records:** {total_count}

## Raw LLM Output

{insight_text}

---
*This report was generated by the Survey Insight System for QA review purposes.*
"""

                                            st.download_button(
                                                label="📄 Download as Markdown",
                                                data=markdown_content,
                                                file_name=f"insight_{insight_survey_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md",
                                                mime="text/markdown",
                                                help="Download the raw LLM output in Markdown format for QA review"
                                            )

                                        with col2:
                                            # Prepare JSON content
                                            json_content = {
                                                "survey_id": insight_survey_id,
                                                "query": user_query,
                                                "raw_llm_output": insight_text,
                                                "execution_time_seconds": execution_time,
                                                "metadata": {
                                                    "llm_provider": llm_provider,
                                                    "model": model_choice,
                                                    "filtered_count": filtered_count,
                                                    "total_count": total_count,
                                                    "generated_at": datetime.now().isoformat(),
                                                    "filters_applied": result.get("filters_applied", {}),
                                                    "warning": result.get("warning", None)
                                                },
                                                "qa_review": {
                                                    "status": "pending_review",
                                                    "reviewer": "",
                                                    "review_date": "",
                                                    "comments": "",
                                                    "approved": False
                                                }
                                            }

                                            st.download_button(
                                                label="📊 Download as JSON",
                                                data=json.dumps(json_content, indent=2),
                                                file_name=f"insight_{insight_survey_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                                                mime="application/json",
                                                help="Download the raw LLM output in JSON format for QA review"
                                            )

                                    # Always show performance metrics
                                    with st.expander("⏱️ Performance Metrics", expanded=False):
                                        col1, col2, col3, col4 = st.columns(4)
                                        with col1:
                                            st.metric("Execution Time", f"{execution_time}s")
                                        with col2:
                                            st.metric("LLM Provider", llm_provider)
                                        with col3:
                                            st.metric("Model Used", model_choice)
                                        with col4:
                                            st.metric("Records Analyzed", filtered_count)

                                    # Show error details if present
                                    if result.get("error"):
                                        st.error(f"❌ Additional error occurred (took {execution_time}s)")
                                        with st.expander("🔧 Error Details", expanded=False):
                                            st.write("**Error message:**")
                                            st.code(result.get("error", "Unknown error"))
                                            st.write("**Troubleshooting tips:**")
                                            st.write("• Check your database connection")
                                            st.write("• Verify the survey ID exists")
                                            st.write("• Ensure your API keys are valid")
                                            st.write("• Try a simpler query")


                                    # '''
                                    # # Check if we have responses for this survey in session state
                                    # if "survey_responses" in st.session_state and st.session_state.survey_responses:
                                    #     # Filter responses by survey_id
                                    #     survey_responses = [r for r in st.session_state.survey_responses if r.get('survey_id') == insight_survey_id]

                                    #     if not survey_responses:
                                    #         st.warning(f"No responses found for survey ID: {insight_survey_id}")
                                    #         st.info("Try loading responses from a file or using a different survey ID.")
                                    #     else:
                                    #         # Create a custom JSON encoder for MongoDB objects
                                    #         class CustomJSONEncoder(json.JSONEncoder):
                                    #             def default(self, obj):
                                    #                 if isinstance(obj, datetime):
                                    #                     return obj.isoformat()
                                    #                 return super().default(obj)

                                    #         # Extract filters from the query
                                    #         filters = {}

                                    #         # Extract age range
                                    #         import re
                                    #         age_range_match = re.search(r'aged\s+(\d+)(?:\s*[-–]\s*(\d+))?', user_query, re.IGNORECASE)
                                    #         if age_range_match:
                                    #             min_age = int(age_range_match.group(1))
                                    #             max_age = int(age_range_match.group(2)) if age_range_match.group(2) else min_age
                                    #             filters['age_min'] = min_age
                                    #             filters['age_max'] = max_age

                                    #         # Extract region
                                    #         region_match = re.search(r'in\s+([A-Za-z\s]+)(?:\s+responded|$)', user_query)
                                    #         if region_match:
                                    #             filters['region'] = region_match.group(1).strip()

                                    #         # Apply filters
                                    #         filtered_responses = survey_responses

                                    #         ## Filter by age range
                                    #         # if 'age_min' in filters and 'age_max' in filters:
                                    #         #     filtered_responses = [
                                    #         #         r for r in filtered_responses
                                    #         #         if r.get('age') is not None and
                                    #         #         filters['age_min'] <= r.get('age', 0) <= filters['age_max']
                                    #         #     ]

                                    #         # # Filter by region
                                    #         # if 'region' in filters:
                                    #         #     region_pattern = re.compile(filters['region'], re.IGNORECASE)
                                    #         #     filtered_responses = [
                                    #         #         r for r in filtered_responses
                                    #         #         if r.get('region') and region_pattern.search(r.get('region', ''))
                                    #         #     ]

                                    #         if not filtered_responses:
                                    #             st.warning("No responses match the filter criteria")
                                    #             st.json({"filters_applied": filters})
                                    #         else:
                                    #             # Convert responses to JSON string
                                    #             response_data = json.dumps(filtered_responses, cls=CustomJSONEncoder, indent=2)

                                    #             # Generate insights using OpenAI
                                    #             prompt = f"""
                                    #             You are an expert data analyst specializing in survey analysis.
                                    #             Analyze the following survey responses and provide insights based on this query: "{user_query}"

                                    #             Survey ID: {insight_survey_id}
                                    #             Number of responses: {len(filtered_responses)}

                                    #             Survey Responses:
                                    #             {response_data}

                                    #             Provide a detailed analysis with key insights, patterns, and trends.
                                    #             Format your response in markdown with clear sections and bullet points where appropriate.
                                    #             """

                                    #             openai_client = openai.OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

                                    #             response = openai_client.chat.completions.create(
                                    #                 model=model_choice,
                                    #                 messages=[
                                    #                     {"role": "system", "content": prompt},
                                    #                     {"role": "user", "content": user_query}
                                    #                 ]
                                    #             )

                                    #             # Extract the insight
                                    #             insight = response.choices[0].message.content

                                    #             # Display the insight
                                    #             st.subheader("Survey Insights")
                                    #             st.markdown(insight)

                                    #             # Display metadata
                                    #             with st.expander("Analysis Metadata", expanded=False):
                                    #                 metadata = {
                                    #                     "survey_id": insight_survey_id,
                                    #                     "query": user_query,
                                    #                     "filtered_count": len(filtered_responses),
                                    #                     "total_count": len(survey_responses),
                                    #                     "filters_applied": filters,
                                    #                     "model": model_choice,
                                    #                     "temperature": temperature,
                                    #                     "max_tokens": max_tokens,
                                    #                     "generated_at": datetime.now().isoformat()
                                    #                 }
                                    #                 st.json(metadata)

                                    #                 # Add a button to download the insight as markdown
                                    #                 st.download_button(
                                    #                     label="Download Insight as Markdown",
                                    #                     data=insight,
                                    #                     file_name=f"insight_{insight_survey_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md",
                                    #                     mime="text/markdown"
                                    #                 )

                                    #                 # Add a button to download the metadata as JSON
                                    #                 st.download_button(
                                    #                     label="Download Metadata as JSON",
                                    #                     data=json.dumps(metadata, indent=2),
                                    #                     file_name=f"metadata_{insight_survey_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                                    #                     mime="application/json",
                                    #                     key="download_metadata"
                                    #                 )
                                    # else:
                                    #     st.warning("No survey responses found in memory")
                                    #     st.info("Try loading responses from a file first using the 'Load Survey Responses' section below.")
                                    # '''
                                except Exception as e:
                                    st.error(f"Error generating insights: {str(e)}")
                                    st.exception(e)

                # Tab 2: Survey Summary
                with insight_tabs[1]:
                    st.subheader("Survey Summary")
                    st.markdown("""
                    Get a summary of demographic information and response patterns for a specific survey.
                    """)

                    # Survey ID input
                    summary_survey_id = st.text_input(
                        "Survey ID",
                        placeholder="Enter the survey ID to summarize (e.g., survey_341)",
                        key="summary_survey_id"
                    )

                    # Generate summary button
                    if st.button("Generate Summary"):
                        if not summary_survey_id:
                            st.error("Survey ID is required")
                        else:
                            with st.spinner("Generating survey summary..."):
                                try:
                                    # Check if we have responses for this survey in session state
                                    if "survey_responses" in st.session_state and st.session_state.survey_responses:
                                        # Filter responses by survey_id
                                        survey_responses = [r for r in st.session_state.survey_responses if r.get('survey_id') == summary_survey_id]

                                        if not survey_responses:
                                            st.warning(f"No responses found for survey ID: {summary_survey_id}")
                                            st.info("Try loading responses from a file or using a different survey ID.")
                                        else:
                                            # Process demographic information
                                            age_groups = {
                                                "18-25": 0,
                                                "26-35": 0,
                                                "36-45": 0,
                                                "46+": 0,
                                                "Unknown": 0
                                            }

                                            regions = {}

                                            for response in survey_responses:
                                                # Process age
                                                age = response.get("age")
                                                if age is None:
                                                    age_groups["Unknown"] += 1
                                                elif age <= 25:
                                                    age_groups["18-25"] += 1
                                                elif age <= 35:
                                                    age_groups["26-35"] += 1
                                                elif age <= 45:
                                                    age_groups["36-45"] += 1
                                                else:
                                                    age_groups["46+"] += 1

                                                # Process region
                                                region = response.get("region", "Unknown")
                                                if region:
                                                    regions[region] = regions.get(region, 0) + 1
                                                else:
                                                    regions["Unknown"] = regions.get("Unknown", 0) + 1

                                            # Sort regions by count (descending)
                                            sorted_regions = dict(sorted(regions.items(), key=lambda x: x[1], reverse=True))

                                            # Limit to top 5 regions + "Other"
                                            if len(sorted_regions) > 5:
                                                top_regions = dict(list(sorted_regions.items())[:5])
                                                other_count = sum(list(sorted_regions.values())[5:])
                                                top_regions["Other"] = other_count
                                                sorted_regions = top_regions

                                            # Create summary
                                            summary = {
                                                "survey_id": summary_survey_id,
                                                "total_responses": len(survey_responses),
                                                "demographics": {
                                                    "age_groups": age_groups,
                                                    "regions": sorted_regions
                                                },
                                                "generated_at": datetime.now().isoformat()
                                            }

                                            # Display summary
                                            st.subheader("Survey Summary")

                                            # Display total responses
                                            st.metric("Total Responses", len(survey_responses))

                                            # Display age distribution
                                            st.subheader("Age Distribution")
                                            age_data = pd.DataFrame({
                                                "Age Group": list(age_groups.keys()),
                                                "Count": list(age_groups.values())
                                            })
                                            st.bar_chart(age_data.set_index("Age Group"))

                                            # Display region distribution
                                            st.subheader("Region Distribution")
                                            region_data = pd.DataFrame({
                                                "Region": list(sorted_regions.keys()),
                                                "Count": list(sorted_regions.values())
                                            })
                                            st.bar_chart(region_data.set_index("Region"))

                                            # Display raw summary data
                                            with st.expander("Raw Summary Data", expanded=False):
                                                st.json(summary)

                                                # Add a button to download the summary as JSON
                                                st.download_button(
                                                    label="Download Summary as JSON",
                                                    data=json.dumps(summary, indent=2),
                                                    file_name=f"summary_{summary_survey_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                                                    mime="application/json"
                                                )
                                    else:
                                        st.warning("No survey responses found in memory")
                                        st.info("Try loading responses from a file first using the 'Load Survey Responses' section below.")
                                except Exception as e:
                                    st.error(f"Error generating summary: {str(e)}")
                                    st.exception(e)

                # Tab 3: MongoDB Connection
                with insight_tabs[2]:
                    st.subheader("MongoDB Connection")
                    st.markdown("""
                    Connect to a MongoDB database to retrieve survey responses.
                    """)

                    # MongoDB connection form
                    with st.form("mongodb_connection_form"):
                        # MongoDB URI input
                        mongodb_uri = st.text_input(
                            "MongoDB URI",
                            placeholder="************************************:port/database",
                            type="password"
                        )

                        # Database name input
                        db_name = st.text_input(
                            "Database Name",
                            value="survey_dev_db"
                        )

                        # Collection name input
                        collection_name = st.text_input(
                            "Collection Name",
                            value="teaching_quality"
                        )

                        # Survey ID input
                        mongo_survey_id = st.text_input(
                            "Survey ID (Optional)",
                            placeholder="Enter survey ID to filter responses"
                        )

                        # Submit button
                        mongo_connect_button = st.form_submit_button("Connect and Load Responses")

                    if mongo_connect_button:
                        if not mongodb_uri:
                            st.error("MongoDB URI is required")
                        else:
                            with st.spinner("Connecting to MongoDB and loading responses..."):
                                try:
                                    # Import pymongo
                                    import pymongo
                                    from pymongo import MongoClient
                                    from bson import ObjectId

                                    # Connect to MongoDB
                                    client = MongoClient(mongodb_uri)
                                    db = client[db_name]
                                    collection = db[collection_name]

                                    # Create query
                                    query = {}
                                    if mongo_survey_id:
                                        query["survey_id"] = mongo_survey_id

                                    # Get responses
                                    cursor = collection.find(query)
                                    mongo_responses = list(cursor)

                                    # Convert ObjectId to string
                                    for response in mongo_responses:
                                        if "_id" in response and isinstance(response["_id"], ObjectId):
                                            response["_id"] = str(response["_id"])

                                    if not mongo_responses:
                                        st.warning(f"No responses found in MongoDB collection '{collection_name}'")
                                        if mongo_survey_id:
                                            st.info(f"No responses found for survey ID: {mongo_survey_id}")
                                    else:
                                        # Initialize survey_responses if not already done
                                        if "survey_responses" not in st.session_state:
                                            st.session_state.survey_responses = []

                                        # Add the loaded responses to the list
                                        st.session_state.survey_responses.extend(mongo_responses)

                                        # Get unique survey IDs
                                        survey_ids = set(r.get('survey_id', 'unknown') for r in mongo_responses)

                                        st.success(f"Loaded {len(mongo_responses)} responses from MongoDB")
                                        st.info(f"Available survey IDs: {', '.join(survey_ids)}")

                                        # Display sample response
                                        if mongo_responses:
                                            with st.expander("Sample Response", expanded=False):
                                                st.json(mongo_responses[0])
                                except Exception as e:
                                    st.error(f"Error connecting to MongoDB: {str(e)}")
                                    st.exception(e)

                # Add a section for loading responses from file
        #         st.divider()
        #         st.subheader("Load Survey Responses from File")

        #         # Add a button to load responses from local file
        #         if st.button("Load Responses from File", key="insight_load_responses"):
        #             success, result = load_responses_from_file()
        #             if success:
        #                 # Initialize survey_responses if not already done
        #                 if "survey_responses" not in st.session_state:
        #                     st.session_state.survey_responses = []

        #                 # Add the loaded responses to the list
        #                 st.session_state.survey_responses.extend(result)

        #                 # Get unique survey IDs
        #                 survey_ids = set(r.get('survey_id', 'unknown') for r in st.session_state.survey_responses)

        #                 st.success(f"Loaded {len(result)} responses from file")
        #                 st.info(f"Available survey IDs: {', '.join(survey_ids)}")
        #             else:
        #                 st.warning(result)

        #         # Display current responses in memory
        #         if "survey_responses" in st.session_state and st.session_state.survey_responses:
        #             with st.expander("Current Responses in Memory", expanded=False):
        #                 # Get unique survey IDs and counts
        #                 survey_counts = {}
        #                 for r in st.session_state.survey_responses:
        #                     survey_id = r.get('survey_id', 'unknown')
        #                     survey_counts[survey_id] = survey_counts.get(survey_id, 0) + 1

        #                 # Display counts
        #                 st.write(f"Total responses: {len(st.session_state.survey_responses)}")
        #                 st.write("Responses by survey ID:")
        #                 for survey_id, count in survey_counts.items():
        #                     st.write(f"- {survey_id}: {count} responses")

        #                 # Add a button to clear responses
        #                 if st.button("Clear All Responses"):
        #                     st.session_state.survey_responses = []
        #                     st.success("All responses cleared from memory")
        #                     st.experimental_rerun()

        #     # Allow manual entry of questions and answers
        #     # st.subheader("Manual Entry")

        #     # with st.form("manual_survey_form"):
        #     #     # Number of questions to add
        #     #     num_manual_questions = st.number_input(
        #     #         "Number of Questions",
        #     #         min_value=1,
        #     #         max_value=10,
        #     #         value=1
        #     #     )

        #     #     manual_answers = []

        #     #     for i in range(num_manual_questions):
        #     #         st.markdown(f"**Question {i+1}**")

        #     #         question = st.text_area(
        #     #             "Question",
        #     #             key=f"manual_question_{i}"
        #     #         )

        #     #         answer = st.text_area(
        #     #             "Answer",
        #     #             key=f"manual_answer_{i}"
        #     #         )

        #     #         if question and answer:
        #     #             manual_answers.append({
        #     #                 "question": question,
        #     #                 "answer": answer
        #     #             })

        #     #     # Submit button
        #     #     manual_submit_button = st.form_submit_button("Submit Manual Responses")

        #     #     if manual_submit_button:
        #     #         if not survey_id:
        #     #             st.error("Survey ID is required")
        #     #         elif not user_id:
        #     #             st.error("User ID is required")
        #     #         elif not manual_answers:
        #     #             st.error("At least one question-answer pair is required")
        #     #         else:
        #     #             # Create the response object
        #     #             response = {
        #     #                 "survey_id": survey_id,
        #     #                 "user_id": user_id,
        #     #                 "age": age if age > 0 else None,
        #     #                 "region": region if region else None,
        #     #                 "answers": manual_answers
        #     #             }

        #     #             # Store the response
        #     #             st.session_state.survey_responses.append(response)

        #     #             # Update the current response
        #     #             st.session_state.current_response = {
        #     #                 "survey_id": survey_id,
        #     #                 "user_id": user_id,
        #     #                 "age": age if age > 0 else None,
        #     #                 "region": region if region else None,
        #     #                 "answers": []
        #     #             }

        #     #             # Show success message
        #     #             st.success("Survey responses submitted successfully!")

        #     #             # Show the response ID
        #     #             response_id = f"response-{uuid.uuid4().hex[:8]}"
        #     #             st.info(f"Response ID: {response_id}")

        #     #             # Store the response with the ID
        #     #             response["id"] = response_id
        #     #             response["created_at"] = time.time()

        #     #             # Auto-save responses to file
        #     #             success, message = save_responses_to_file(st.session_state.survey_responses)
        #     #             if success:
        #     #                 st.info(message)

        #     #             # Display the response as JSON
        #     #             st.json(response)

        # # Display stored responses with search functionality
        # # if st.session_state.survey_responses:
        # #     with st.expander("View Stored Responses", expanded=False):
        # #         # Add search functionality
        # #         st.subheader("Search Responses")
        # #         search_col1, search_col2 = st.columns(2)

        # #         with search_col1:
        # #             search_survey_id = st.text_input("Search by Survey ID", placeholder="Enter survey ID...")

        # #         with search_col2:
        # #             search_user_id = st.text_input("Search by User ID", placeholder="Enter user ID...")

        # #         # Filter responses based on search criteria
        # #         filtered_responses = st.session_state.survey_responses

        # #         if search_survey_id:
        # #             filtered_responses = [r for r in filtered_responses if search_survey_id.lower() in r.get('survey_id', '').lower()]

        # #         if search_user_id:
        # #             filtered_responses = [r for r in filtered_responses if search_user_id.lower() in r.get('user_id', '').lower()]

        #         # # Display the number of results
        #         # st.write(f"Found {len(filtered_responses)} responses")

        #         # # Display responses as JSON
        #         # for i, response in enumerate(filtered_responses):
        #         #     with st.expander(f"Response {i+1} - ID: {response.get('id', 'N/A')}", expanded=False):
        #         #         # Display as JSON
        #         #         st.json(response)

        #         #         # Add a button to download the response as JSON
        #         #         if st.button(f"Download Response {i+1}", key=f"download_{i}"):
        #         #             # Create a JSON string
        #         #             json_str = json.dumps(response, indent=2)
        #         #             # Create a download button
        #         #             st.download_button(
        #         #                 label=f"Download Response {i+1} as JSON",
        #         #                 data=json_str,
        #         #                 file_name=f"response_{response.get('id', 'unknown')}.json",
        #         #                 mime="application/json",
        #         #                 key=f"download_button_{i}"
        #         #             )

        #         # Add a button to download all responses as JSON
        #         if st.button("Download All Responses"):
        #             # Create a JSON string with all responses
        #             all_json_str = json.dumps(filtered_responses, indent=2)
        #             # Create a download button
        #             st.download_button(
        #                 label="Download All Responses as JSON",
        #                 data=all_json_str,
        #                 file_name="all_responses.json",
        #                 mime="application/json",
        #                 key="download_all_button"
        #             )

        #         # Add a button to save responses to a local file
        #         if st.button("Save Responses to Local File"):
        #             success, message = save_responses_to_file(filtered_responses)
        #             if success:
        #                 st.success(message)
        #             else:
        #                 st.error(message)

# Tab 2: Question Generator-API
if selected_tab == "Question Generator-API":
    with tab2_container:
        st.header("Question Generator API")

    # Create a radio button for API subtabs
    if "active_api_tab" not in st.session_state:
        st.session_state.active_api_tab = "Generate Questions API"
        clear_displayed_questions()

    # Use a radio button to select API tabs
    api_selected_tab = st.radio(
        "Select API:",
        ["Generate Questions API", "Submit Response API", "View Responses", "Survey Insights API", "Get Session API", "List Sessions API"],
        horizontal=True,
        key="api_tab_selector",
        index=3
    )

    # Check if API tab has changed
    if api_selected_tab != st.session_state.active_api_tab:
        st.session_state.active_api_tab = api_selected_tab
        clear_displayed_questions()

    # Create containers for each API tab
    api_tab1_container = st.container()  # Generate Questions API
    api_tab2_container = st.container()  # Submit Response API
    api_tab3_container = st.container()  # View Responses
    api_tab4_container = st.container()  # Survey Insights API
    api_tab5_container = st.container()  # Get Session API
    api_tab6_container = st.container()  # List Sessions API

    # Generate Questions API
    if api_selected_tab == "Generate Questions API":
        with api_tab1_container:
            st.subheader("POST /generate/")
            st.markdown("Generate survey questions based on context and language.")

            with st.form("api_generate_form"):
                api_context = st.text_area(
                    "Context",
                    height=150,
                    placeholder="Enter context for survey questions..."
                )

                api_language = st.selectbox("Language", LANGUAGES, key="api_language")

                api_session_id = st.text_input(
                    "Session ID (Optional)",
                    placeholder="Enter session ID to continue a thread..."
                )

                api_generate_button = st.form_submit_button("Send Request")

        if api_generate_button and api_context and api_language:
            with st.spinner("Sending request..."):
                # Prepare request data
                request_data = {
                    "context": api_context,
                    "language": api_language
                }

                # Add session_id if provided
                if api_session_id:
                    request_data["session_id"] = api_session_id

                # Call the generate_questions_from_llm function
                try:
                    # Extract number of questions
                    num_questions = extract_num_questions(api_context)
                    st.info(f"Will generate exactly {num_questions} questions based on your context")

                    # Get messages if session_id is provided
                    messages = None
                    is_continued_session = False
                    if api_session_id:
                        session = get_session(api_session_id)
                        if session:
                            messages = session["messages"]
                            is_continued_session = True
                            st.info(f"Using existing session: {api_session_id}")
                        else:
                            st.warning(f"Session ID {api_session_id} not found. Creating a new session.")

                    # Generate questions
                    questions = generate_questions_from_llm(
                        context=api_context,
                        language=api_language,
                        num_questions=num_questions,
                        messages=messages
                    )

                    # Create or update session
                    if is_continued_session:
                        # Update existing session
                        update_session(api_session_id, api_context, questions)
                    else:
                        # Create new session
                        new_session_id = create_session(api_context, api_language)
                        update_session(new_session_id, "", questions)
                        api_session_id = new_session_id

                    # Create a deep copy of the questions to avoid reference issues
                    display_questions = questions.copy()

                    # Store the questions for display in this tab
                    st.session_state.display_questions = display_questions

                    # Prepare response with only the newly generated questions
                    response = {
                        "session_id": api_session_id,
                        "questions": display_questions,  # Only the newly generated questions
                        "is_continued_session": is_continued_session,
                        "metadata": {
                            "language": api_language,
                            "num_questions": len(display_questions),
                            "model": "gpt-4.1"
                        }
                    }

                    # Display response
                    st.subheader("Response")
                    st.json(response)

                    # Add a button to use this session in the Question Generator
                    if st.button("Use this session in Question Generator"):
                        st.session_state.current_session_id = api_session_id
                        st.success(f"Session {api_session_id} will be used in Question Generator tab")

                except Exception as e:
                    st.error(f"Error: {str(e)}")
                    st.json({
                        "error": str(e),
                        "status_code": 500,
                        "detail": "An error occurred while processing your request"
                    })

    # Submit Response API
    if api_selected_tab == "Submit Response API":
        with api_tab2_container:
            st.subheader("POST /responses/")
            st.markdown("Submit a survey response with answers to questions.")

            with st.form("api_submit_response_form"):
                api_survey_id = st.text_input(
                    "Survey ID",
                    placeholder="Enter the survey ID (e.g., session-12345678)"
                )

                api_user_id = st.text_input(
                    "User ID",
                    placeholder="Enter a unique identifier for the respondent"
                )

                # Demographic information
                col1, col2 = st.columns(2)
                with col1:
                    api_age = st.number_input(
                        "Age",
                        min_value=0,
                        max_value=120,
                        value=0,
                        help="Enter the respondent's age (optional)"
                    )

                with col2:
                    api_region = st.text_input(
                        "Region",
                        placeholder="Enter the respondent's geographic region (optional)"
                    )

                # Number of answers to add
                num_answers = st.number_input(
                    "Number of Answers",
                    min_value=1,
                    max_value=10,
                    value=1
                )

                # Questions and answers
                api_answers = []
                for i in range(num_answers):
                    st.markdown(f"**Answer {i+1}**")

                    question = st.text_area(
                        "Question",
                        key=f"api_question_{i}",
                        placeholder="Enter the question"
                    )

                    answer = st.text_area(
                        "Answer",
                        key=f"api_answer_{i}",
                        placeholder="Enter the answer"
                    )

                    if question and answer:
                        api_answers.append({
                            "question": question,
                            "answer": answer
                        })

                submit_response_button = st.form_submit_button("Send Request")

            if submit_response_button:
                if not api_survey_id:
                    st.error("Survey ID is required")
                elif not api_user_id:
                    st.error("User ID is required")
                elif not api_answers:
                    st.error("At least one question-answer pair is required")
                else:
                    with st.spinner("Sending request..."):
                        try:
                            # Prepare request data
                            request_data = {
                                "survey_id": api_survey_id,
                                "user_id": api_user_id,
                                "age": api_age if api_age > 0 else None,
                                "region": api_region if api_region else None,
                                "answers": api_answers
                            }

                            # Generate a response ID
                            response_id = f"response-{uuid.uuid4().hex[:8]}"

                            # Prepare response
                            response = {
                                "id": response_id,
                                "message": "Survey response submitted successfully",
                                "survey_id": api_survey_id,
                                "user_id": api_user_id,
                                "num_answers": len(api_answers)
                            }

                            # Display response
                            st.subheader("Response")
                            st.json(response)

                            # Store the response in session state
                            full_response = {
                                "id": response_id,
                                "survey_id": api_survey_id,
                                "user_id": api_user_id,
                                "age": api_age if api_age > 0 else None,
                                "region": api_region if api_region else None,
                                "answers": api_answers,
                                "created_at": time.time()
                            }

                            # Initialize survey_responses if not already done
                            if "survey_responses" not in st.session_state:
                                st.session_state.survey_responses = []

                            # Add the response to the list
                            st.session_state.survey_responses.append(full_response)

                            # Auto-save responses to file
                            success, message = save_responses_to_file(st.session_state.survey_responses)

                            # Show success message
                            st.success("Survey response submitted successfully!")
                            if success:
                                st.info(message)

                        except Exception as e:
                            st.error(f"Error: {str(e)}")
                            st.json({
                                "error": str(e),
                                "status_code": 500,
                                "detail": "An error occurred while processing your request"
                            })

    # View Responses
    if api_selected_tab == "View Responses":
        with api_tab3_container:
            st.subheader("GET /responses/")
            st.markdown("View and search stored survey responses.")

            # Add search functionality
            st.subheader("Search Responses")
            search_col1, search_col2 = st.columns(2)

            with search_col1:
                api_search_survey_id = st.text_input("Search by Survey ID", placeholder="Enter survey ID...", key="api_search_survey_id")

            with search_col2:
                api_search_user_id = st.text_input("Search by User ID", placeholder="Enter user ID...", key="api_search_user_id")

            # Add a button to load responses from local file
            if st.button("Load Responses from Local File"):
                success, result = load_responses_from_file()
                if success:
                    # Initialize survey_responses if not already done
                    if "survey_responses" not in st.session_state:
                        st.session_state.survey_responses = []

                    # Add the loaded responses to the list
                    st.session_state.survey_responses.extend(result)

                    st.success(f"Loaded {len(result)} responses from file")
                else:
                    st.warning(result)

            # Filter responses based on search criteria
            if "survey_responses" in st.session_state and st.session_state.survey_responses:
                filtered_responses = st.session_state.survey_responses

                if api_search_survey_id:
                    filtered_responses = [r for r in filtered_responses if api_search_survey_id.lower() in r.get('survey_id', '').lower()]

                if api_search_user_id:
                    filtered_responses = [r for r in filtered_responses if api_search_user_id.lower() in r.get('user_id', '').lower()]

                # Display the number of results
                st.write(f"Found {len(filtered_responses)} responses")

                # Create a JSON response object
                response_obj = {
                    "count": len(filtered_responses),
                    "responses": filtered_responses
                }

                # Display as JSON
                st.subheader("Response")
                st.json(response_obj)

                # Add a button to download all responses as JSON
                if st.button("Download All Responses", key="api_download_all"):
                    # Create a JSON string with all responses
                    all_json_str = json.dumps(response_obj, indent=2)
                    # Create a download button
                    st.download_button(
                        label="Download All Responses as JSON",
                        data=all_json_str,
                        file_name="all_responses.json",
                        mime="application/json",
                        key="api_download_all_button"
                    )
            else:
                st.info("No responses found. Submit responses first or load from a local file.")

    # Survey Insights API
    if api_selected_tab == "Survey Insights API":
        with api_tab4_container:
            st.subheader("POST /insights/")
            # st.markdown("Generate insights from survey responses based on a user query.")
            col1, col2 = st.columns((0.3,0.7))
            with col1:
                # LLM Provider Selection
                api_llm_provider = st.radio(
                    "Model Selection",
                    ["OpenAI", "Gemini"],
                    index=0,
                    horizontal=True,
                    help="Choose the AI model provider",
                    key="api_llm_provider"
                )
            with col2:
                # Advanced options
                with st.expander("Advanced Options", expanded=False):


                    # Model Selection and Parameters based on provider
                    if api_llm_provider == "OpenAI":
                        api_model_choice = st.selectbox(
                            "OpenAI Model",
                            ["gpt-4o", "gpt-4o-mini", "gpt-3.5-turbo"],
                            index=0,
                            help="Select OpenAI model variant",
                            key="api_model_choice"
                        )

                        api_temperature = st.slider(
                            "Temperature",
                            min_value=0.0,
                            max_value=2.0,
                            value=0.3,
                            step=0.1,
                            help="Higher values make output more random, lower values more deterministic",
                            key="api_temperature"
                        )

                        api_max_tokens = st.slider(
                            "Max Tokens",
                            min_value=500,
                            max_value=4000,
                            value=1500,
                            step=100,
                            help="Maximum length of the generated insight",
                            key="api_max_tokens"
                        )

                        api_top_p = st.slider(
                            "Top P",
                            min_value=0.0,
                            max_value=1.0,
                            value=1.0,
                            step=0.1,
                            help="Nucleus sampling parameter",
                            key="api_top_p"
                        )

                    else:  # Gemini
                        api_model_choice = st.selectbox(
                            "Gemini Model",
                            ["gemini-1.5-pro", "gemini-1.5-flash", "gemini-1.0-pro"],
                            index=0,
                            help="Select Gemini model variant",
                            key="api_model_choice"
                        )

                        api_temperature = st.slider(
                            "Temperature",
                            min_value=0.0,
                            max_value=1.0,
                            value=0.4,
                            step=0.1,
                            help="Controls randomness in Gemini responses",
                            key="api_temperature"
                        )

                        api_max_tokens = st.slider(
                            "Max Output Tokens",
                            min_value=100,
                            max_value=2048,
                            value=1024,
                            step=50,
                            help="Maximum number of tokens in Gemini response",
                            key="api_max_tokens"
                        )

                        api_top_p = st.slider(
                            "Top P",
                            min_value=0.0,
                            max_value=1.0,
                            value=0.95,
                            step=0.05,
                            help="Nucleus sampling for Gemini",
                            key="api_top_p"
                        )

                        api_top_k = st.slider(
                            "Top K",
                            min_value=1,
                            max_value=100,
                            value=40,
                            step=1,
                            help="Top-k sampling for Gemini",
                            key="api_top_k"
                        )
            with st.form("api_insights_form"):
                # Survey ID input
                api_insight_survey_id = st.text_input(
                    "Survey ID",
                    value="survey_341",
                    placeholder="Enter the survey ID to analyze (e.g., session-12345678)"
                )

                # User query input
                api_user_query = st.text_area(
                    "Query",
                    value="Summarize how users aged 18-25 in New York responded to questions about teaching quality.",
                    placeholder="Example: Summarize how users aged 18-25 in New York responded to questions about teaching quality.",
                    height=100
                )


                # Submit button
                api_insight_button = st.form_submit_button("Generate Insights")
                # Survey ID to collection name mapping
            SURVEY_COLLECTIONS = {
                "survey_342": "ev_survey_2025",
                "survey_345": "remore_work",
                "survey_343": "AI_tools",
                "survey_341": "teaching_quality",
                "survey_344": "online_payments"
            }

            # Default collection name if survey_id is not in the mapping
            DEFAULT_COLLECTION = "teaching_quality"
            if api_insight_button:
                if not api_insight_survey_id:
                    st.error("Survey ID is required")
                elif not api_user_query:
                    st.error("Query is required")
                else:
                    with st.spinner("Analyzing survey responses..."):
                        try:
                            import time
                            start_time = time.time()

                            # Initialize the SurveyInsightGenerator
                            generator = SurveyInsightGenerator(
                                mongo_uri=os.environ.get("MONGO_URI"),
                                db_name=os.environ.get("MONGODB_DB_NAME", "survey_dev_db"),
                                # collection_name=os.environ.get("MONGODB_COLLECTION_NAME", "teaching_quality"),
                                collection_name = SURVEY_COLLECTIONS.get(api_insight_survey_id, DEFAULT_COLLECTION),
                                openai_api_key=os.environ.get("OPENAI_API_KEY")
                            )

                            # Generate insights
                            result = generator.generate_insight(api_insight_survey_id, api_user_query)

                            end_time = time.time()
                            execution_time = round(end_time - start_time, 2)

                            # Prepare API response
                            api_response = {
                                "survey_id": api_insight_survey_id,
                                "query": api_user_query,
                                "insight": result.get("insight"),
                                "execution_time_seconds": execution_time,
                                "metadata": {
                                    "filtered_count": result.get("filtered_count", 0),
                                    "total_count": result.get("total_count", 0),
                                    "filters_applied": result.get("filters_applied", {}),
                                    "model": api_model_choice,
                                    "llm_provider": api_llm_provider,
                                    "temperature": api_temperature,
                                    "max_tokens": api_max_tokens,
                                    "generated_at": datetime.now().isoformat()
                                }
                            }

                            # Handle different result scenarios for API
                            insight_text = result.get("insight", "").strip()
                            filtered_count = result.get("filtered_count", 0)
                            total_count = result.get("total_count", 0)

                            # Add status and result classification to API response
                            if not insight_text:
                                api_response["status"] = "no_data"
                                api_response["message"] = "No insight generated - no survey data found"
                                api_response["suggestions"] = [
                                    "Verify the survey ID exists",
                                    "Check database connection",
                                    "Ensure survey has responses"
                                ]
                            elif filtered_count == 0:
                                api_response["status"] = "no_matches"
                                api_response["message"] = "No records matched the query filters"
                                api_response["suggestions"] = [
                                    "Broaden search criteria",
                                    "Check age ranges and location filters",
                                    "Verify survey ID is correct"
                                ]
                            elif insight_text.startswith("NO RELEVANT DATA:"):
                                # Primary detection: LLM explicitly flagged the query
                                api_response["status"] = "generic_query"
                                api_response["message"] = "Query issue detected by AI - too generic or not specific enough"
                                api_response["ai_feedback"] = insight_text
                                api_response["detection_method"] = "llm_analysis"
                                api_response["suggestions"] = [
                                    "Make your query more specific",
                                    "Ask about particular aspects like 'teacher satisfaction' or 'student engagement'",
                                    "Focus on specific demographics or topics",
                                    "Use example queries provided for this survey type"
                                ]
                                api_response["example_queries"] = get_example_queries(api_insight_survey_id)
                            elif is_generic_or_irrelevant_query(api_user_query):
                                # Fallback detection: Only for extremely generic queries the LLM might miss
                                api_response["status"] = "test_query"
                                api_response["message"] = "Query appears to be a test or extremely generic"
                                api_response["detection_method"] = "pattern_matching"
                                api_response["suggestions"] = [
                                    "Try asking a specific question about the survey data",
                                    "Focus on particular aspects like demographics, satisfaction, or specific topics"
                                ]
                                api_response["example_queries"] = get_example_queries(api_insight_survey_id)
                            elif "no relevant" in insight_text.lower() or "cannot find" in insight_text.lower() or "no data" in insight_text.lower() or "not available" in insight_text.lower():
                                api_response["status"] = "no_relevant_data"
                                api_response["message"] = "Query not answerable from available survey data"
                                api_response["suggestions"] = [
                                    "Rephrase query to match survey topics",
                                    "Ask about different aspects covered in survey",
                                    "Check if question is within survey scope"
                                ]
                            else:
                                api_response["status"] = "success"
                                api_response["message"] = "Insight generated successfully"

                            # Add warning if present
                            if "warning" in result:
                                api_response["warning"] = result["warning"]

                            # Add error if present
                            if "error" in result and result["error"]:
                                api_response["error"] = result["error"]
                                api_response["status"] = "error"

                            # Display appropriate message based on status
                            status = api_response.get("status", "unknown")
                            if status == "success":
                                st.success(f"✅ API Response generated successfully in {execution_time} seconds")
                            elif status == "generic_query":
                                st.warning(f"⚠️ {api_response['message']} (took {execution_time}s)")
                                st.warning("🔍 **Query Not Specific Enough**")

                                # Show AI feedback if available
                                if api_response.get("ai_feedback") and api_response["ai_feedback"].startswith("NO RELEVANT DATA:"):
                                    st.info("🤖 **AI Analysis:**")
                                    st.write(api_response["ai_feedback"])

                                st.write("**Your query needs to be more specific to provide meaningful insights:**")
                                st.write("• Instead of 'test query', ask about specific aspects like 'teacher satisfaction' or 'student engagement'")
                                st.write("• Try queries like: 'How do students rate teaching quality?' or 'What are the main concerns about curriculum?'")
                                st.write("• Focus on specific demographics: 'How do users aged 25-35 feel about remote work?'")
                                st.write("• Ask about particular topics covered in the survey")

                                # Show example queries based on survey type
                                with st.expander("💡 Example Queries for This Survey", expanded=True):
                                    if api_response.get("example_queries"):
                                        for example in api_response["example_queries"]:
                                            st.write(f"• {example}")
                                    else:
                                        # Fallback examples
                                        survey_examples = get_example_queries(api_insight_survey_id)
                                        for example in survey_examples:
                                            st.write(f"• {example}")

                                # Show additional API suggestions
                                st.write("**API-specific suggestions:**")
                                for suggestion in api_response.get("suggestions", []):
                                    st.write(f"• {suggestion}")
                            elif status == "test_query":
                                st.warning(f"⚠️ {api_response['message']} (took {execution_time}s)")
                                st.warning("🔍 **Test Query Detected**")
                                st.write("**Your query appears to be a test or extremely generic:**")

                                # Show example queries
                                with st.expander("💡 Example Queries for This Survey", expanded=True):
                                    if api_response.get("example_queries"):
                                        for example in api_response["example_queries"]:
                                            st.write(f"• {example}")

                                # Show API suggestions
                                st.write("**Suggestions:**")
                                for suggestion in api_response.get("suggestions", []):
                                    st.write(f"• {suggestion}")
                            elif status in ["no_data", "no_matches", "no_relevant_data"]:
                                st.warning(f"⚠️ {api_response['message']} (took {execution_time}s)")
                                st.info("🔍 **API returned 'No Results Found' status**")

                                # Show suggestions
                                st.write("**Suggestions:**")
                                for suggestion in api_response.get("suggestions", []):
                                    st.write(f"• {suggestion}")
                            else:
                                st.error(f"❌ API Error (took {execution_time}s)")

                            # Display response
                            st.subheader("API Response")
                            st.json(api_response)

                            # If insight was generated successfully, display it in a more readable format
                            if result.get("insight") and status == "success":
                                with st.expander("📋 View Formatted Insight", expanded=False):
                                    st.markdown(result["insight"])

                                # Add download buttons for API QA review
                                st.write("\n**📥 Download Raw API Response for QA Review:**")
                                col1, col2 = st.columns(2)

                                with col1:
                                    # Prepare Markdown content for API
                                    api_markdown_content = f"""# API Survey Insight Analysis Report

## API Request Information
- **Survey ID:** {api_insight_survey_id}
- **Query:** {api_user_query}
- **Generated At:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **Execution Time:** {execution_time} seconds

## API Configuration
- **LLM Provider:** {api_llm_provider}
- **Model:** {api_model_choice}
- **Temperature:** {api_temperature}
- **Max Tokens:** {api_max_tokens}
- **Records Analyzed:** {filtered_count}
- **Total Records:** {total_count}

## API Response Status
- **Status:** {status}
- **Message:** {api_response.get('message', 'N/A')}

## Raw LLM Output

{result.get("insight", "No insight generated")}

## Complete API Response

```json
{json.dumps(api_response, indent=2)}
```

---
*This report was generated by the Survey Insight System API for QA review purposes.*
"""

                                    st.download_button(
                                        label="📄 Download API as Markdown",
                                        data=api_markdown_content,
                                        file_name=f"api_insight_{api_insight_survey_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md",
                                        mime="text/markdown",
                                        help="Download the raw API response in Markdown format for QA review"
                                    )

                                with col2:
                                    # Prepare enhanced JSON content for API
                                    api_json_content = {
                                        "api_request": {
                                            "survey_id": api_insight_survey_id,
                                            "query": api_user_query,
                                            "llm_provider": api_llm_provider,
                                            "model": api_model_choice,
                                            "temperature": api_temperature,
                                            "max_tokens": api_max_tokens
                                        },
                                        "api_response": api_response,
                                        "raw_llm_output": result.get("insight", "No insight generated"),
                                        "execution_time_seconds": execution_time,
                                        "qa_review": {
                                            "status": "pending_review",
                                            "reviewer": "",
                                            "review_date": "",
                                            "comments": "",
                                            "approved": False,
                                            "api_endpoint": "POST /insights/generate"
                                        }
                                    }

                                    st.download_button(
                                        label="📊 Download API as JSON",
                                        data=json.dumps(api_json_content, indent=2),
                                        file_name=f"api_insight_{api_insight_survey_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                                        mime="application/json",
                                        help="Download the raw API response in JSON format for QA review"
                                    )

                            elif result.get("insight") and status == "no_relevant_data":
                                with st.expander("📄 AI Response (No Relevant Data Found)", expanded=False):
                                    st.markdown(result["insight"])

                            # Always display performance metrics for API
                            with st.expander("⏱️ API Performance Metrics", expanded=False):
                                col1, col2, col3, col4 = st.columns(4)
                                with col1:
                                    st.metric("Execution Time", f"{execution_time}s")
                                with col2:
                                    st.metric("LLM Provider", api_llm_provider)
                                with col3:
                                    st.metric("Model Used", api_model_choice)
                                with col4:
                                    st.metric("Records Analyzed", filtered_count)

                            # Show additional metrics
                            if status != "success":
                                with st.expander("🔧 Troubleshooting Information", expanded=False):
                                    st.write("**Query Analysis:**")
                                    st.write(f"• Survey ID: {api_insight_survey_id}")
                                    st.write(f"• Query: {api_user_query}")
                                    st.write(f"• Records found: {filtered_count}")
                                    st.write(f"• Total records: {total_count}")
                                    st.write(f"• Status: {status}")

                                    if result.get("filters_applied"):
                                        st.write("**Filters applied:**")
                                        for filter_key, filter_value in result["filters_applied"].items():
                                            st.write(f"• {filter_key}: {filter_value}")

                        except Exception as e:
                            st.error(f"Error generating insights: {str(e)}")
                            st.json({
                                "error": str(e),
                                "status_code": 500,
                                "detail": "An error occurred while processing your request"
                            })

    # Get Session API
    if api_selected_tab == "Get Session API":
        with api_tab5_container:
            st.subheader("GET /sessions/{session_id}")
            st.markdown("Get information about a specific session.")

            with st.form("api_get_session_form"):
                get_session_id = st.text_input(
                    "Session ID",
                    placeholder="Enter the session ID to retrieve"
                )

                get_session_button = st.form_submit_button("Send Request")

        if get_session_button and get_session_id:
            with st.spinner("Sending request..."):
                try:
                    # Get session
                    session = get_session(get_session_id)

                    if session:
                        # Create context summary
                        context_summary = session["context"][:100]
                        if len(session["context"]) > 100:
                            context_summary += "..."

                        # Prepare response
                        response = {
                            "session_id": get_session_id,
                            "language": session["language"],
                            "created_at": session["created_at"],
                            "last_accessed": session["last_accessed"],
                            "questions": session["questions"],  # Return the current questions
                            "question_count": len(session["questions"]),
                            "context_summary": context_summary
                        }

                        # Display response
                        st.subheader("Response")
                        st.json(response)

                        # Add a button to use this session in the Question Generator
                        if st.button("Use this session in Question Generator"):
                            st.session_state.current_session_id = get_session_id
                            st.success(f"Session {get_session_id} will be used in Question Generator tab")
                    else:
                        st.error(f"Session with ID {get_session_id} not found or expired")
                        st.json({
                            "error": "Not Found",
                            "status_code": 404,
                            "detail": f"Session with ID {get_session_id} not found or expired"
                        })
                except Exception as e:
                    st.error(f"Error: {str(e)}")
                    st.json({
                        "error": str(e),
                        "status_code": 500,
                        "detail": "An error occurred while processing your request"
                    })

    # List Sessions API
    if api_selected_tab == "List Sessions API":
        with api_tab6_container:
            st.subheader("GET /sessions/")
            st.markdown("List all active sessions.")

            list_sessions_button = st.button("Send Request")

        if list_sessions_button:
            with st.spinner("Sending request..."):
                try:
                    # Get all sessions
                    sessions_info = []
                    for session_id, session in st.session_state.sessions.items():
                        # Create context summary
                        context_summary = session["context"][:100]
                        if len(session["context"]) > 100:
                            context_summary += "..."

                        sessions_info.append({
                            "session_id": session_id,
                            "language": session["language"],
                            "created_at": session["created_at"],
                            "last_accessed": session["last_accessed"],
                            "questions": session["questions"],  # Include the current questions
                            "question_count": len(session["questions"]),
                            "context_summary": context_summary
                        })

                    # Prepare response
                    response = {
                        "sessions": sessions_info,
                        "count": len(sessions_info)
                    }

                    # Display response
                    st.subheader("Response")
                    st.json(response)
                except Exception as e:
                    st.error(f"Error: {str(e)}")
                    st.json({
                        "error": str(e),
                        "status_code": 500,
                        "detail": "An error occurred while processing your request"
                    })

# Footer
st.divider()
st.markdown(
    """
    <div style="text-align: center">
        <p>Survey Insight System - Standalone App using GPT-4.1</p>
    </div>
    """,
    unsafe_allow_html=True
)
