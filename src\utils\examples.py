"""
API documentation examples for the Survey Insight System.
"""

# API documentation examples
examples = {
    "question_generator": {
        "summary": "Generate survey questions",
        "description": "Generate survey questions based on context and language",
        "value": {
            "context": "Create a customer satisfaction survey for a software product focusing on ease of use, feature completeness, and customer support experience.",
            "language": "English"
        }
    },
    "response_generator": {
        "summary": "Submit survey responses",
        "description": "Submit responses to survey questions with demographic information",
        "value": {
            "survey_id": "session-12345678",
            "user_id": "user-987654",
            "age": 30,
            "region": "New York",
            "answers": [
                {
                    "question": "How satisfied are you with the product's ease of use?",
                    "answer": "Very satisfied. The interface is intuitive and I rarely need to consult documentation."
                },
                {
                    "question": "What features would you like to see added to the product?",
                    "answer": "I would like to see better integration with third-party tools and improved reporting capabilities."
                }
            ]
        }
    },
    "insight_generator": {
        "summary": "Generate insights from survey responses",
        "description": "Analyze survey responses and generate insights based on user query",
        "value": {
            "survey_id": "survey_341",
            "user_query": "Summarize how users aged 18-25 in New York responded to questions about ease of use."
        }
    }
}
