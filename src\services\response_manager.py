"""
Service for managing survey responses.
This module provides functions for storing, retrieving, and analyzing survey responses.
"""

from typing import List, Dict, Any, Optional
from app.models.survey import SurveyResponseSubmission, StoredSurveyResponse
from app.db.json_db import db as json_db
from app.db.vector_db import vector_db
from app.services.embedding import embedding_service

class ResponseManager:
    """Service for managing survey responses."""
    
    def store_response(self, response: SurveyResponseSubmission) -> str:
        """
        Store a survey response.
        
        Args:
            response: The survey response to store
            
        Returns:
            The ID of the stored response
        """
        # Convert the response to a StoredSurveyResponse
        stored_response = StoredSurveyResponse(
            survey_id=response.survey_id,
            user_id=response.user_id,
            age=response.age,
            region=response.region,
            answers=[{"question": a.question, "answer": a.answer} for a in response.answers]
        )
        
        # Convert to dictionary for storage
        response_dict = stored_response.dict()
        
        # Generate a vector embedding for the response
        embedding = embedding_service.generate_response_embedding(response_dict)
        
        # Store the embedding in the vector database
        vector_id = vector_db.add_vector(
            vector=embedding,
            metadata={
                "response_id": stored_response.id,
                "survey_id": stored_response.survey_id,
                "user_id": stored_response.user_id
            }
        )
        
        # Update the response with the vector ID
        response_dict["vector_id"] = vector_id
        
        # Store the response in the JSON database
        json_db.add_response(response_dict)
        
        return stored_response.id
    
    def get_response(self, response_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a survey response by ID.
        
        Args:
            response_id: The ID of the response to retrieve
            
        Returns:
            The response if found, None otherwise
        """
        return json_db.get_response(response_id)
    
    def get_responses_by_survey(self, survey_id: str) -> List[Dict[str, Any]]:
        """
        Get all responses for a specific survey.
        
        Args:
            survey_id: The ID of the survey
            
        Returns:
            A list of responses for the specified survey
        """
        return json_db.get_responses_by_survey(survey_id)
    
    def get_responses_by_user(self, user_id: str) -> List[Dict[str, Any]]:
        """
        Get all responses from a specific user.
        
        Args:
            user_id: The ID of the user
            
        Returns:
            A list of responses from the specified user
        """
        return json_db.get_responses_by_user(user_id)
    
    def get_all_responses(self) -> List[Dict[str, Any]]:
        """
        Get all survey responses.
        
        Returns:
            A list of all responses in the database
        """
        return json_db.get_all_responses()
    
    def find_similar_responses(self, response_id: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """
        Find responses similar to the specified response.
        
        Args:
            response_id: The ID of the response to find similar responses for
            top_k: The number of similar responses to return
            
        Returns:
            A list of similar responses
        """
        # Get the response
        response = json_db.get_response(response_id)
        if not response:
            return []
        
        # Get the vector ID
        vector_id = response.get("vector_id")
        if not vector_id:
            return []
        
        # Get the vector
        vector_entry = vector_db.get_vector(vector_id)
        if not vector_entry:
            return []
        
        # Get the vector
        vector = vector_entry.get("vector", [])
        if not vector:
            return []
        
        # Search for similar vectors
        similar_vectors = vector_db.search_similar_vectors(vector, top_k + 1)
        
        # Get the responses for the similar vectors
        similar_responses = []
        for vector_result in similar_vectors:
            # Skip the original response
            if vector_result.get("id") == vector_id:
                continue
            
            # Get the response ID from the metadata
            response_id = vector_result.get("metadata", {}).get("response_id")
            if not response_id:
                continue
            
            # Get the response
            similar_response = json_db.get_response(response_id)
            if not similar_response:
                continue
            
            # Add the similarity score
            similar_response["similarity"] = vector_result.get("similarity", 0.0)
            
            # Add to the list
            similar_responses.append(similar_response)
            
            # Stop if we have enough responses
            if len(similar_responses) >= top_k:
                break
        
        return similar_responses

# Create a singleton instance of the response manager
response_manager = ResponseManager()
