from fastapi import APIRouter, HTTPException, status, Body
from app.models.survey import SurveyRequest, SurveyResponse, SurveyResponseSubmission, StoredSurveyResponse
from app.services.llm_client import generate_questions_from_llm
from app.services.session_manager import session_manager
from app.services.response_manager import response_manager
from app.utils.helpers import extract_num_questions
from app.utils.examples import examples
from app.exceptions import BadRequestException, LLMException, NotFoundException
import logging
from typing import List, Dict, Any
from app.api.sessions import *

# Configure logger
logger = logging.getLogger(__name__)

router = APIRouter()

@router.post("/responses",
             response_model=Dict[str, Any],
             status_code=status.HTTP_201_CREATED,
             summary="Submit survey responses",
             description="Submit responses to survey questions with demographic information",
             response_description="Returns confirmation with response ID",
             tags=["2. Response Generator"])
async def submit_survey_response(
    response: SurveyResponseSubmission = Body(..., examples=[examples["response_generator"]["value"]])
):
    """
    Submit a survey response.

    This endpoint allows users to submit their answers to survey questions.
    The response includes demographic information (age, region) and a list of question-answer pairs.

    Example:
    ```json
    {
        "survey_id": "session-12345678",
        "user_id": "user-987654",
        "age": 30,
        "region": "New York",
        "answers": [
            {
                "question": "How satisfied are you with the product?",
                "answer": "Very satisfied"
            }
        ]
    }
    ```
    """
    try:
        # Validate the response
        if not response.survey_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Survey ID is required"
            )

        if not response.user_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User ID is required"
            )

        if not response.answers or len(response.answers) == 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="At least one answer is required"
            )

        # Store the response
        response_id = response_manager.store_response(response)

        # Return the response ID
        return {
            "id": response_id,
            "message": "Survey response submitted successfully",
            "survey_id": response.survey_id,
            "user_id": response.user_id,
            "num_answers": len(response.answers)
        }
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        # Log the error
        logger.exception("Error submitting survey response")

        # Return a 500 error
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to submit survey response: {str(e)}"
        )

@router.get("/responses/{response_id}",
            response_model=Dict[str, Any],
            summary="Get a survey response",
            description="Retrieve a specific survey response by its ID",
            response_description="Returns the complete survey response",
            tags=["2. Response Generator"])
async def get_survey_response(response_id: str):
    """
    Get a survey response by ID.

    This endpoint retrieves a specific survey response by its ID.

    Example:
    ```
    GET /generate/responses/response-12345678
    ```
    """
    try:
        # Get the response
        response = response_manager.get_response(response_id)

        # Check if the response exists
        if not response:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Survey response with ID {response_id} not found"
            )

        # Return the response
        return response
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        # Log the error
        logger.exception(f"Error retrieving survey response {response_id}")

        # Return a 500 error
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve survey response: {str(e)}"
        )

@router.get("/surveys/{survey_id}/responses",
            response_model=List[Dict[str, Any]],
            summary="Get all survey responses",
            description="Retrieve all responses for a specific survey",
            response_description="Returns a list of all survey responses",
            tags=["2. Response Generator"])
async def get_survey_responses(survey_id: str):
    """
    Get all responses for a specific survey.

    This endpoint retrieves all responses for a specific survey.

    Example:
    ```
    GET /generate/surveys/session-12345678/responses
    ```
    """
    try:
        # Get the responses
        responses = response_manager.get_responses_by_survey(survey_id)

        # Return the responses
        return responses
    except Exception as e:
        # Log the error
        logger.exception(f"Error retrieving responses for survey {survey_id}")

        # Return a 500 error
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve survey responses: {str(e)}"
        )