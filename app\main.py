from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import APIKeyHeader
from app.api import generator, sessions, insights, responses
from app.exceptions import (
    SurveyInsightException,
    survey_insight_exception_handler,
    validation_exception_handler,
    general_exception_handler
)
import logging
from typing import Dict, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Hard-coded API token for security
API_TOKEN = "survey-insight-system-token-2025"
api_key_header = APIKeyHeader(name="X-API-Key")

# Security dependency
async def verify_api_key(api_key: str = Depends(api_key_header)):
    if api_key != API_TOKEN:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid API Key",
            headers={"WWW-Authenticate": "ApiKey"},
        )
    return api_key

app = FastAPI(
    title="Survey Insight System"
    # description="""
    # API for generating survey questions, collecting responses, and analyzing insights.

    # ## Authentication

    # All endpoints are protected with API Key authentication.
    # Include the API Key in the `X-API-Key` header with each request.

    # ## Endpoint Categories

    # 1. **Question Generator** - Generate survey questions based on context
    # 2. **Response Generator** - Submit and retrieve survey responses
    # 3. **Insight Creator** - Generate insights from survey responses
    # 4. **Sessions** - Manage survey sessions

    # ## Example Usage

    # ```python
    # import requests

    # API_URL = "http://localhost:8000"
    # API_KEY = "survey-insight-system-token-2025"

    # # Generate questions
    # response = requests.post(
    #     f"{API_URL}/generate/",
    #     json={
    #         "context": "Create a customer satisfaction survey for a software product",
    #         "language": "English"
    #     },
    #     headers={"X-API-Key": API_KEY}
    # )

    # # Submit responses
    # response = requests.post(
    #     f"{API_URL}/generate/responses",
    #     json={
    #         "survey_id": "session-12345678",
    #         "user_id": "user-987654",
    #         "answers": [
    #             {
    #                 "question": "How satisfied are you with the product?",
    #                 "answer": "Very satisfied"
    #             }
    #         ]
    #     },
    #     headers={"X-API-Key": API_KEY}
    # )

    # # Generate insights
    # response = requests.post(
    #     f"{API_URL}/insights/generate",
    #     json={
    #         "survey_id": "session-12345678",
    #         "user_query": "Summarize the overall satisfaction levels"
    #     },
    #     headers={"X-API-Key": API_KEY}
    # )
    # ```
    # """,
    # version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Register routers in the specified order
app.include_router(
    generator.router,
    prefix="/generate",
    tags=["1. Question Generator"],
    dependencies=[Depends(verify_api_key)]
)
app.include_router(
    responses.router,
    prefix="/responses",
    tags=["2. Response Generator"],
    dependencies=[Depends(verify_api_key)]
)
app.include_router(
    insights.router,
    prefix="/insights",
    tags=["3. Insight Creator"],
    dependencies=[Depends(verify_api_key)]
)
# app.include_router(
#     sessions.router,
#     prefix="/sessions",
#     tags=["4. Sessions"],
#     dependencies=[Depends(verify_api_key)]
# )

# Register exception handlers
app.add_exception_handler(SurveyInsightException, survey_insight_exception_handler)
app.add_exception_handler(RequestValidationError, validation_exception_handler)
app.add_exception_handler(Exception, general_exception_handler)