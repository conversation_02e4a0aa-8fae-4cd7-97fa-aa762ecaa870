---
title: Survey Insight System
emoji: 📊
colorFrom: blue
colorTo: purple
sdk: streamlit
sdk_version: 1.28.0
app_file: standalone_app.py
pinned: false
---

# Survey Insight System with GPT-4.1

A Streamlit application for generating survey questions based on context and language using GPT-4.1, with session management for continued conversations.

## Features

- **Question Generation**: Generate survey questions based on context and language using GPT-4.1
- **Language Support**: Support for multiple languages
- **Session Management**: Maintain context between requests with session support
- **Chat Thread**: Continue the same conversation thread for follow-up questions
- **Smart Question Count**: Intelligently extracts the number of questions to generate from your context (defaults to 5 if not specified)

## How to Use

1. **Enter Context**: Provide the context for your survey questions in the text area
2. **Select Language**: Choose the language for your questions from the dropdown
3. **Toggle Chat Thread**: Enable to continue the same chat thread, disable to start a new one
4. **Generate Questions**: Click the button to generate questions based on your input
5. **View Results**: See the generated questions and session information
6. **API Explorer**: Use the API Explorer tab to directly interact with the API endpoints:
   - Generate Questions API: Create survey questions with optional session continuation
   - Get Session API: Retrieve information about a specific session
   - List Sessions API: View all active sessions

## Environment Variables

This application requires an OpenAI API key to function. Set the following environment variable:

- `OPENAI_API_KEY`: Your OpenAI API key

## Local Development

1. Clone the repository
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```
3. Set your OpenAI API key as an environment variable
4. Run the Streamlit app:
   ```bash
   streamlit run standalone_app.py
   ```

   Or use the provided scripts:
   ```bash
   # On Unix/Mac
   ./run_standalone.sh

   # On Windows
   run_standalone.bat
   ```

## Deployment on Hugging Face Spaces

This application is configured for deployment on Hugging Face Spaces using Streamlit. To deploy:

1. **Create a new Space on Hugging Face:**
   - Go to [Hugging Face Spaces](https://huggingface.co/spaces)
   - Click "Create new Space"
   - Choose "Streamlit" as the SDK
   - Set the app file to `app.py`

2. **Upload your files:**
   - Upload all files from this repository to your Space
   - Make sure `app.py` is set as the main application file

3. **Set environment variables:**
   - Go to Settings > Repository Secrets
   - Add the following secrets:
     - `OPENAI_API_KEY`: Your OpenAI API key
     - `MONGO_URI`: Your MongoDB connection string (if using MongoDB features)
     - `MONGODB_DB_NAME`: Your MongoDB database name

4. **Deploy:**
   - The app will automatically build and deploy
   - Access your app at: `https://your-username-your-space-name.hf.space`

### Local Testing for Hugging Face

To test the Hugging Face configuration locally:

```bash
# Install dependencies
pip install -r requirements.txt

# Run the app as it would run on Hugging Face
streamlit run app.py --server.port=7860 --server.address=0.0.0.0
```

Or use the provided script:
```bash
./run_local.sh
```

Then access: http://localhost:7860