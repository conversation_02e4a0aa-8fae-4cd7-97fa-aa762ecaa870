# Extract number of questions, clean inputs, etc.
import re
import json
import logging
from word2number import w2n
from openai import OpenAI
import os

# Configure logger
logger = logging.getLogger(__name__)

# Initialize OpenAI client (reusing the same client as in llm_client.py)
api_key = os.getenv("OPENAI_API_KEY")
client = OpenAI(api_key=api_key) if api_key else None

def extract_num_questions_regex(context: str) -> int:
    """Extract number of questions using regex pattern matching.

    This is a fallback method if the LLM-based extraction fails.
    """
    match = re.search(r"(\b(?:\w+|\d+)\b)\s+(questions|survey questions)", context, re.IGNORECASE)
    if match:
        number_str = match.group(1)
        try:
            return int(number_str)
        except ValueError:
            try:
                return w2n.word_to_num(number_str)
            except ValueError:
                pass
    return 5

def extract_num_questions(context: str) -> int:
    """Extract the number of questions to generate from the context.

    First tries to use the LLM to intelligently determine the number,
    then falls back to regex pattern matching if that fails.

    Args:
        context: The survey context

    Returns:
        The number of questions to generate (default: 5)
    """
    # If OpenAI client is not available, use regex fallback
    if not client:
        logger.warning("OpenAI client not available, using regex fallback for question count")
        return extract_num_questions_regex(context)

    try:
        # Create a prompt for the LLM to extract the number of questions
        prompt = f"""
Analyze the following survey context and determine how many questions should be generated.
If a specific number is mentioned (like "5 questions" or "ten survey questions"), use that number.
If a range is given, use the average of that range.
If no number is specified, default number of questions to 5.
Return ONLY a JSON object with a single key "num_questions" and an integer value.

Context: "{context}"

Example response: {{"num_questions": 5}}
"""

        # Make the API call
        logger.info("Using LLM to determine number of questions")
        response = client.chat.completions.create(
            model="gpt-4.1",
            messages=[
                {"role": "system", "content": "You analyze text to extract numerical information."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.1,  # Low temperature for more deterministic results
            max_tokens=50     # Short response needed
        )

        # Parse the response
        content = response.choices[0].message.content

        try:
            # Try to parse the JSON response
            result = json.loads(content)
            if isinstance(result, dict) and "num_questions" in result:
                num_questions = int(result["num_questions"])
                logger.info(f"LLM determined {num_questions} questions should be generated")

                # Ensure a reasonable range (between 1 and 20)
                num_questions = max(1, min(20, num_questions))
                return num_questions
        except (json.JSONDecodeError, ValueError, TypeError) as e:
            logger.warning(f"Failed to parse LLM response for question count: {e}")

    except Exception as e:
        logger.warning(f"Error using LLM to determine question count: {e}")

    # If LLM method fails, fall back to regex
    logger.info("Falling back to regex pattern matching for question count")
    return extract_num_questions_regex(context)