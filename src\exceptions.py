"""
Survey Insight System - Exception Handlers
"""
from fastapi import Request, status
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from typing import Union, Dict, Any, Optional


class SurveyInsightException(Exception):
    """Base exception for Survey Insight System."""
    def __init__(self, message: str, status_code: int = 400):
        self.message = message
        self.status_code = status_code
        super().__init__(self.message)


class BadRequestException(SurveyInsightException):
    """Exception for bad requests."""
    def __init__(self, detail: str, field: str = "", suggestion: str = ""):
        self.detail = detail
        self.field = field
        self.suggestion = suggestion
        super().__init__(detail, status.HTTP_400_BAD_REQUEST)


class NotFoundException(SurveyInsightException):
    """Exception for not found resources."""
    def __init__(self, detail: str, field: str = "", suggestion: str = ""):
        self.detail = detail
        self.field = field
        self.suggestion = suggestion
        super().__init__(detail, status.HTTP_404_NOT_FOUND)


class LLMException(SurveyInsightException):
    """Exception for LLM-related errors."""
    def __init__(self, detail: str, field: str = "", suggestion: str = ""):
        self.detail = detail
        self.field = field
        self.suggestion = suggestion
        super().__init__(detail, status.HTTP_500_INTERNAL_SERVER_ERROR)


async def survey_insight_exception_handler(request: Request, exc: SurveyInsightException) -> JSONResponse:
    """Handle SurveyInsightException."""
    content = {
        "error": exc.message,
        "status_code": exc.status_code
    }

    # Add additional fields for specific exception types
    if hasattr(exc, 'detail'):
        content["detail"] = exc.detail
    if hasattr(exc, 'field') and exc.field:
        content["field"] = exc.field
    if hasattr(exc, 'suggestion') and exc.suggestion:
        content["suggestion"] = exc.suggestion

    return JSONResponse(
        status_code=exc.status_code,
        content=content
    )


async def validation_exception_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
    """Handle validation errors."""
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={
            "error": "Validation error",
            "status_code": status.HTTP_422_UNPROCESSABLE_ENTITY,
            "detail": exc.errors()
        }
    )


async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """Handle general exceptions."""
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "error": str(exc),
            "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR,
            "detail": "An unexpected error occurred"
        }
    )
