#!/bin/bash

echo "Starting Survey Insight System (OOP Version)..."

# Check if we should run both services or just one
if [ "$1" = "ui-only" ]; then
    echo "Starting Streamlit UI only..."
    streamlit run app_oop.py --server.port=7860 --server.address=0.0.0.0
elif [ "$1" = "api-only" ]; then
    echo "Starting FastAPI server only..."
    uvicorn app.main:app --host 0.0.0.0 --port 8000
else
    # Start both services (default)
    echo "Starting both FastAPI server and Streamlit UI..."

    # Start FastAPI server in background
    echo "Starting FastAPI server..."
    uvicorn app.main:app --host 0.0.0.0 --port 8000 &
    FASTAPI_PID=$!

    # Wait for FastAPI server to start
    sleep 3

    # Start Streamlit UI with OOP app
    echo "Starting Streamlit UI (OOP Version)..."
    streamlit run app_oop.py --server.port=7860 --server.address=0.0.0.0 &
    STREAMLIT_PID=$!

    echo "Both services started!"
    echo "FastAPI server: http://0.0.0.0:8000"
    echo "FastAPI docs: http://0.0.0.0:8000/docs"
    echo "Streamlit UI: http://0.0.0.0:7860"
    echo ""
    echo "The Streamlit UI includes both UI and API documentation/testing!"

    # Handle termination signals
    trap "echo 'Stopping services...'; kill $FASTAPI_PID $STREAMLIT_PID; exit" SIGINT SIGTERM

    # Keep the container running
    wait
fi
