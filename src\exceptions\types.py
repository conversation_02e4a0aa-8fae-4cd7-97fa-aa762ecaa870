from .base import SurveyInsightException


class ValidationException(SurveyInsightException):
    """Exception raised for validation errors."""

    def __init__(
        self,
        detail: str = "The provided data is invalid.",
        field: str = "",
        suggestion: str = "Please check the input data and try again."
    ):
        super().__init__(
            status_code=422,
            key="validation_error",
            detail=detail,
            field=field,
            suggestion=suggestion
        )


class BadRequestException(SurveyInsightException):
    """Exception raised for bad request errors."""

    def __init__(
        self,
        detail: str = "The request is invalid.",
        field: str = "",
        suggestion: str = "Please check your request and try again."
    ):
        super().__init__(
            status_code=400,
            key="bad_request",
            detail=detail,
            field=field,
            suggestion=suggestion
        )


class NotFoundException(SurveyInsightException):
    """Exception raised when a resource is not found."""

    def __init__(
        self,
        detail: str = "The requested resource was not found.",
        field: str = "",
        suggestion: str = "Please check the resource identifier and try again."
    ):
        super().__init__(
            status_code=404,
            key="not_found",
            detail=detail,
            field=field,
            suggestion=suggestion
        )


class LLMException(SurveyInsightException):
    """Exception raised for LLM-related errors."""

    def __init__(
        self,
        detail: str = "An error occurred with the language model.",
        field: str = "",
        suggestion: str = "Please try again later or contact support."
    ):
        super().__init__(
            status_code=502,
            key="llm_error",
            detail=detail,
            field=field,
            suggestion=suggestion
        )


class DatabaseException(SurveyInsightException):
    """Exception raised for database-related errors."""

    def __init__(
        self,
        detail: str = "A database error occurred.",
        field: str = "",
        suggestion: str = "Please try again later or contact support."
    ):
        super().__init__(
            status_code=500,
            key="database_error",
            detail=detail,
            field=field,
            suggestion=suggestion
        )


class ConfigurationException(SurveyInsightException):
    """Exception raised for configuration-related errors."""

    def __init__(
        self,
        detail: str = "A configuration error occurred.",
        field: str = "",
        suggestion: str = "Please check the application configuration."
    ):
        super().__init__(
            status_code=500,
            key="configuration_error",
            detail=detail,
            field=field,
            suggestion=suggestion
        )


class AuthenticationException(SurveyInsightException):
    """Exception raised for authentication errors."""

    def __init__(
        self,
        detail: str = "Authentication failed.",
        field: str = "",
        suggestion: str = "Please check your credentials and try again."
    ):
        super().__init__(
            status_code=401,
            key="authentication_error",
            detail=detail,
            field=field,
            suggestion=suggestion
        )


class AuthorizationException(SurveyInsightException):
    """Exception raised for authorization errors."""

    def __init__(
        self,
        detail: str = "You do not have permission to perform this action.",
        field: str = "",
        suggestion: str = "Please contact an administrator for access."
    ):
        super().__init__(
            status_code=403,
            key="authorization_error",
            detail=detail,
            field=field,
            suggestion=suggestion
        )


class RateLimitException(SurveyInsightException):
    """Exception raised when rate limits are exceeded."""

    def __init__(
        self,
        detail: str = "Rate limit exceeded.",
        field: str = "",
        suggestion: str = "Please try again later."
    ):
        super().__init__(
            status_code=429,
            key="rate_limit_error",
            detail=detail,
            field=field,
            suggestion=suggestion
        )


class ServiceUnavailableException(SurveyInsightException):
    """Exception raised when a service is unavailable."""

    def __init__(
        self,
        detail: str = "The service is currently unavailable.",
        field: str = "",
        suggestion: str = "Please try again later."
    ):
        super().__init__(
            status_code=503,
            key="service_unavailable",
            detail=detail,
            field=field,
            suggestion=suggestion
        )
