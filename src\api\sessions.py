from fastapi import APIRouter, <PERSON>, Body
from pydantic import BaseModel, <PERSON>
from typing import List, Dict, Any, Optional
from app.services.session_manager import session_manager
from app.exceptions import NotFoundException
import logging

# Configure logger
logger = logging.getLogger(__name__)

router = APIRouter()

class SessionInfo(BaseModel):
    """Information about a session."""
    session_id: str = Field(description="The session ID")
    language: str = Field(description="The language used in the session")
    created_at: float = Field(description="Timestamp when the session was created")
    last_accessed: float = Field(description="Timestamp when the session was last accessed")
    question_count: int = Field(description="Number of questions generated in this session")
    context_summary: str = Field(description="Summary of the context used in this session")

@router.get("/{session_id}",
            response_model=SessionInfo,
            summary="Get session information",
            description="Get information about a specific session",
            response_description="Returns session details including language, creation time, and question count")
async def get_session(session_id: str = Path(..., description="The session ID to retrieve")):
    """
    Get information about a specific session.

    Example:
    ```
    GET /sessions/session-12345678
    ```

    Example response:
    ```json
    {
        "session_id": "session-12345678",
        "language": "English",
        "created_at": 1653123456.789,
        "last_accessed": 1653123456.789,
        "question_count": 5,
        "context_summary": "Create a customer satisfaction survey for a software product..."
    }
    ```
    """
    session = session_manager.get_session(session_id)
    if not session:
        raise NotFoundException(
            detail=f"Session with ID {session_id} not found or expired.",
            field="session_id",
            suggestion="Please check the session ID or start a new session."
        )

    # Create a summary of the context (first 100 characters)
    context_summary = session["context"][:100]
    if len(session["context"]) > 100:
        context_summary += "..."

    return SessionInfo(
        session_id=session_id,
        language=session["language"],
        created_at=session["created_at"],
        last_accessed=session["last_accessed"],
        question_count=len(session["questions"]),
        context_summary=context_summary
    )

class SessionsList(BaseModel):
    """List of active sessions."""
    sessions: List[SessionInfo] = Field(description="List of active sessions")
    count: int = Field(description="Total number of active sessions")

@router.get("/",
            response_model=SessionsList,
            summary="List all sessions",
            description="List all active sessions",
            response_description="Returns a list of all active sessions")
async def list_sessions():
    """
    List all active sessions.

    Example:
    ```
    GET /sessions/
    ```

    Example response:
    ```json
    {
        "sessions": [
            {
                "session_id": "session-12345678",
                "language": "English",
                "created_at": 1653123456.789,
                "last_accessed": 1653123456.789,
                "question_count": 5,
                "context_summary": "Create a customer satisfaction survey..."
            },
            {
                "session_id": "session-87654321",
                "language": "Spanish",
                "created_at": 1653123456.789,
                "last_accessed": 1653123456.789,
                "question_count": 3,
                "context_summary": "Crear una encuesta de satisfacción..."
            }
        ],
        "count": 2
    }
    ```
    """
    # Clean expired sessions first
    session_manager.clean_expired_sessions()

    # Get all active sessions
    sessions_info = []
    for session_id, session in session_manager.sessions.items():
        # Create a summary of the context (first 100 characters)
        context_summary = session["context"][:100]
        if len(session["context"]) > 100:
            context_summary += "..."

        sessions_info.append(SessionInfo(
            session_id=session_id,
            language=session["language"],
            created_at=session["created_at"],
            last_accessed=session["last_accessed"],
            question_count=len(session["questions"]),
            context_summary=context_summary
        ))

    return SessionsList(
        sessions=sessions_info,
        count=len(sessions_info)
    )
