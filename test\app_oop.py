"""
Object-Oriented Survey Insight System
A clean, modular implementation using OOP principles
"""

import streamlit as st
import os
import uuid
import time
import json
import re
from datetime import datetime
from typing import List, Dict, Any, Optional
from openai import OpenAI
import pandas as pd
from abc import ABC, abstractmethod
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


class Config:
    """Configuration class for the application"""

    LANGUAGES = [
        "English", "Spanish", "French", "German", "Italian", "Portuguese", "Dutch",
        "Russian", "Chinese", "Japanese", "Korean", "Arabic", "Hindi", "Bengali",
        "Urdu", "Turkish", "Persian", "Thai", "Vietnamese", "Indonesian", "Malay",
        "Swahili", "Polish", "Ukrainian", "Czech", "Slovak", "Hungarian", "Romanian",
        "Bulgarian", "Greek", "Swedish", "Norwegian", "Danish", "Finnish", "Hebrew"
    ]

    SURVEY_COLLECTIONS = {
        "survey_342": "ev_survey_2025",
        "survey_345": "remore_work",
        "survey_343": "AI_tools",
        "survey_341": "teaching_quality",
        "survey_344": "online_payments"
    }

    DEFAULT_COLLECTION = "teaching_quality"
    DEFAULT_NUM_QUESTIONS = 5
    MAX_QUESTIONS = 20
    MIN_QUESTIONS = 1


class OpenAIClient:
    """OpenAI client wrapper for API interactions"""

    def __init__(self, api_key: str):
        if not api_key:
            st.error("No OpenAI API key found. Please set the OPENAI_API_KEY environment variable.")
            st.stop()
        # self.client = OpenAI(api_key=api_key)
        self.client = OpenAI(api_key=api_key)

    def generate_questions(self, context: str, language: str, num_questions: int,
                          messages: Optional[List[Dict[str, str]]] = None) -> List[str]:
        """Generate survey questions using OpenAI"""

        system_prompt = f"""
        You are a survey question generator. Generate EXACTLY {num_questions} brief and relevant
        survey questions in {language} based on the given context.
        Return ONLY the questions as a JSON array of strings with EXACTLY {num_questions} questions.
        Do not include any explanations or additional text.
        Example response format: ["Question 1?", "Question 2?", "Question 3?"]

        IMPORTANT: You must generate EXACTLY {num_questions} questions, no more and no less.
        """

        if messages is None:
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": f"Context: {context}"}
            ]
        else:
            messages.append({"role": "user", "content": f"Additional context: {context}"})

        try:
            response = self.client.chat.completions.create(
                model="gpt-4",
                messages=messages,
                temperature=0.7,
                max_tokens=1000
            )

            content = response.choices[0].message.content
            return self._parse_questions_response(content, num_questions)

        except Exception as e:
            st.error(f"Error generating questions: {str(e)}")
            return [f"Question {i+1}?" for i in range(num_questions)]

    def extract_question_count(self, context: str) -> int:
        """Extract the number of questions to generate from context"""
        try:
            response = self.client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": """
                    Analyze the following survey context and determine how many questions should be generated.
                    If a specific number is mentioned, extract that number.
                    If a range is given, use the average of that range.
                    If no number is specified, return 5.
                    Return ONLY a number as a digit, nothing else.
                    """},
                    {"role": "user", "content": context}
                ],
                temperature=0,
                max_tokens=10
            )

            num_str = response.choices[0].message.content.strip()
            num = int(num_str)
            return max(Config.MIN_QUESTIONS, min(num, Config.MAX_QUESTIONS))

        except Exception:
            return self._extract_question_count_regex(context)

    def _parse_questions_response(self, content: str, num_questions: int) -> List[str]:
        """Parse the OpenAI response to extract questions"""
        try:
            questions_data = json.loads(content)

            if isinstance(questions_data, dict) and "questions" in questions_data:
                questions = questions_data["questions"]
            elif isinstance(questions_data, list):
                questions = questions_data
            else:
                questions = [content]

        except json.JSONDecodeError:
            lines = [line.strip() for line in content.split('\n') if line.strip()]
            questions = lines if lines else [content]

        # Ensure exact number of questions
        if len(questions) > num_questions:
            questions = questions[:num_questions]
        elif len(questions) < num_questions:
            for i in range(len(questions), num_questions):
                questions.append(f"Additional question {i+1}?")

        return questions[:num_questions]

    def _extract_question_count_regex(self, context: str) -> int:
        """Extract question count using regex patterns"""
        patterns = [
            r"(\d+)\s+(?:questions|survey questions|items)",
            r"(one|two|three|four|five|six|seven|eight|nine|ten)\s+(?:questions|survey questions|items)",
            r"generate\s+(\d+)",
            r"(\d+)[\s-]question",
            r"need\s+(\d+)",
            r"create\s+(\d+)"
        ]

        word_to_num = {
            "one": 1, "two": 2, "three": 3, "four": 4, "five": 5,
            "six": 6, "seven": 7, "eight": 8, "nine": 9, "ten": 10
        }

        context_lower = context.lower()

        for pattern in patterns:
            match = re.search(pattern, context_lower)
            if match:
                num_str = match.group(1)
                try:
                    return int(num_str)
                except ValueError:
                    if num_str in word_to_num:
                        return word_to_num[num_str]

        return Config.DEFAULT_NUM_QUESTIONS


class SessionManager:
    """Manages survey sessions and state"""

    def __init__(self):
        self._initialize_session_state()

    def _initialize_session_state(self):
        """Initialize Streamlit session state"""
        if "sessions" not in st.session_state:
            st.session_state.sessions = {}
        if "current_session_id" not in st.session_state:
            st.session_state.current_session_id = None
        if "questions" not in st.session_state:
            st.session_state.questions = []
        if "display_questions" not in st.session_state:
            st.session_state.display_questions = []
        if "chat_history" not in st.session_state:
            st.session_state.chat_history = []
        if "survey_responses" not in st.session_state:
            st.session_state.survey_responses = []

    def create_session(self, context: str, language: str) -> str:
        """Create a new session"""
        session_id = f"session-{uuid.uuid4().hex[:8]}"
        st.session_state.sessions[session_id] = {
            "created_at": time.time(),
            "last_accessed": time.time(),
            "language": language,
            "context": context,
            "messages": [
                {"role": "system", "content": "You generate relevant survey questions in JSON."},
                {"role": "user", "content": f"Context: {context}"}
            ],
            "questions": []
        }

        st.session_state.questions = []
        st.session_state.display_questions = []

        return session_id

    def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get a session by ID"""
        session = st.session_state.sessions.get(session_id)
        if session:
            session["last_accessed"] = time.time()
        return session

    def update_session(self, session_id: str, new_context: str, questions: List[str]) -> None:
        """Update a session with new context and questions"""
        session = self.get_session(session_id)
        if not session:
            return

        if new_context:
            session["context"] += f"\n\nAdditional context: {new_context}"
            session["messages"].append({"role": "user", "content": f"Additional context: {new_context}"})

        question_strings = [str(q) for q in questions]

        if question_strings:
            session["messages"].append({
                "role": "assistant",
                "content": f"Generated questions: {', '.join(question_strings)}"
            })

        session["questions"] = question_strings

    def clear_displayed_questions(self):
        """Clear the questions displayed in the UI"""
        st.session_state.display_questions = []
        st.session_state.questions = []


class FileManager:
    """Handles file operations for responses"""

    @staticmethod
    def save_responses(responses: List[Dict], file_path: str = "data/survey_responses.json") -> tuple[bool, str]:
        """Save survey responses to a local JSON file"""
        try:
            os.makedirs(os.path.dirname(file_path), exist_ok=True)

            with open(file_path, "w") as f:
                json.dump({"responses": responses}, f, indent=2)

            return True, f"Responses saved to {file_path}"
        except Exception as e:
            return False, f"Error saving responses: {str(e)}"

    @staticmethod
    def load_responses(file_path: str = "data/survey_responses.json") -> tuple[bool, Any]:
        """Load survey responses from a local JSON file"""
        try:
            if os.path.exists(file_path):
                with open(file_path, "r") as f:
                    loaded_data = json.load(f)

                if "responses" in loaded_data:
                    return True, loaded_data["responses"]
                else:
                    return False, "No responses found in the file"
            else:
                return False, f"File not found: {file_path}"
        except Exception as e:
            return False, f"Error loading responses: {str(e)}"


class BaseTab(ABC):
    """Abstract base class for application tabs"""

    def __init__(self, session_manager: SessionManager, openai_client: OpenAIClient):
        self.session_manager = session_manager
        self.openai_client = openai_client

    @abstractmethod
    def render(self):
        """Render the tab content"""
        pass


class QuestionGeneratorTab(BaseTab):
    """Question Generator tab implementation"""

    def render(self):
        st.subheader("Generate Survey Questions")

        with st.form("question_generator_form"):
            context = st.text_area(
                "Context for Survey Questions",
                height=200,
                placeholder="Enter the context for generating survey questions..."
            )

            language = st.selectbox("Language", Config.LANGUAGES)
            use_same_thread = st.toggle("Continue same chat thread", value=False)

            if use_same_thread and st.session_state.current_session_id:
                st.info(f"Using session: {st.session_state.current_session_id}")
            elif use_same_thread:
                session_id = st.text_input(
                    "Session ID (Optional)",
                    placeholder="Enter a session ID to continue a thread..."
                )
                if session_id:
                    st.session_state.current_session_id = session_id

            submit_button = st.form_submit_button("Generate Questions")

        if submit_button and context and language:
            self._process_question_generation(context, language, use_same_thread)

        self._display_generated_questions()

    def _process_question_generation(self, context: str, language: str, use_same_thread: bool):
        """Process the question generation request"""
        num_questions = self.openai_client.extract_question_count(context)
        st.info(f"Will generate exactly {num_questions} questions based on your context")

        with st.spinner("Generating questions..."):
            current_session_id = st.session_state.current_session_id if use_same_thread else None
            is_continued_session = False
            messages = None

            if current_session_id:
                session = self.session_manager.get_session(current_session_id)
                if session:
                    language = language or session["language"]
                    messages = session["messages"]
                    is_continued_session = True

            questions = self.openai_client.generate_questions(
                context=context,
                language=language,
                num_questions=num_questions,
                messages=messages
            )

            if is_continued_session:
                self.session_manager.update_session(current_session_id, context, questions)
            else:
                current_session_id = self.session_manager.create_session(context, language)
                self.session_manager.update_session(current_session_id, "", questions)

            st.session_state.current_session_id = current_session_id
            st.session_state.questions = []
            st.session_state.display_questions = questions.copy()

            st.success(f"Generated {num_questions} questions as requested!")
            st.info(f"Session ID: {current_session_id}")
            st.info(f"Continued session: {is_continued_session}")
            st.info(f"Model: GPT-4")

    def _display_generated_questions(self):
        """Display the generated questions"""
        if st.session_state.display_questions:
            st.subheader("Generated Questions")
            for i, question in enumerate(st.session_state.display_questions, 1):
                st.write(f"{i}. {question}")

            with st.expander("View as JSON", expanded=False):
                response = {
                    "session_id": st.session_state.current_session_id,
                    "questions": st.session_state.display_questions,
                    "metadata": {
                        "num_questions": len(st.session_state.display_questions),
                        "model": "gpt-4"
                    }
                }
                st.json(response)


class ResponseGeneratorTab(BaseTab):
    """Response Generator tab implementation"""

    def render(self):
        st.subheader("Submit Survey Responses")

        # Initialize current response state
        if "current_response" not in st.session_state:
            st.session_state.current_response = {
                "survey_id": "",
                "user_id": "",
                "age": None,
                "region": "",
                "answers": []
            }

        survey_id = st.text_input(
            "Survey ID",
            value=st.session_state.current_response.get("survey_id", ""),
            help="Enter the ID of the survey (e.g., session-12345678)"
        )

        user_id = st.text_input(
            "User ID",
            value=st.session_state.current_response.get("user_id", ""),
            help="Enter a unique identifier for the respondent"
        )

        col1, col2 = st.columns(2)
        with col1:
            age = st.number_input(
                "Age",
                min_value=0,
                max_value=120,
                value=st.session_state.current_response.get("age", 0) or 0,
                help="Enter the respondent's age (optional)"
            )

        with col2:
            region = st.text_input(
                "Region",
                value=st.session_state.current_response.get("region", ""),
                help="Enter the respondent's geographic region (optional)"
            )

        st.subheader("Questions and Answers")

        questions = self._get_current_session_questions()

        if questions:
            st.info(f"Using questions from session: {st.session_state.current_session_id}")
            self._render_response_form(questions, survey_id, user_id, age, region)
        else:
            st.warning("No questions available. Please generate questions first in the Question Generator tab.")
            if st.button("Go to Question Generator"):
                st.session_state.active_ui_tab = "Question Generator"
                st.rerun()

    def _get_current_session_questions(self) -> List[str]:
        """Get questions from the current session"""
        if st.session_state.current_session_id:
            session = self.session_manager.get_session(st.session_state.current_session_id)
            if session:
                return session.get("questions", [])
        return []

    def _render_response_form(self, questions: List[str], survey_id: str, user_id: str, age: int, region: str):
        """Render the response form"""
        with st.form("survey_response_form"):
            answers = []

            for i, question in enumerate(questions):
                answer = st.text_area(
                    f"Q{i+1}: {question}",
                    height=100,
                    key=f"answer_{i}"
                )

                if answer:
                    answers.append({
                        "question": question,
                        "answer": answer
                    })

            submit_button = st.form_submit_button("Submit Responses")

            if submit_button:
                self._process_response_submission(survey_id, user_id, age, region, answers)

    def _process_response_submission(self, survey_id: str, user_id: str, age: int, region: str, answers: List[Dict]):
        """Process the response submission"""
        if not survey_id:
            st.error("Survey ID is required")
        elif not user_id:
            st.error("User ID is required")
        elif not answers:
            st.error("At least one answer is required")
        else:
            response = {
                "survey_id": survey_id,
                "user_id": user_id,
                "age": age if age > 0 else None,
                "region": region if region else None,
                "answers": answers,
                "id": f"response-{uuid.uuid4().hex[:8]}",
                "created_at": time.time()
            }

            st.session_state.survey_responses.append(response)

            st.success("Survey responses submitted successfully!")
            st.info(f"Response ID: {response['id']}")

            # Auto-save responses
            success, message = FileManager.save_responses(st.session_state.survey_responses)
            if success:
                st.info(message)

            st.json(response)


class ViewResponsesTab(BaseTab):
    """View Stored Responses tab implementation"""

    def render(self):
        st.subheader("View Stored Responses")

        # Search functionality
        st.subheader("Search Responses")
        search_col1, search_col2 = st.columns(2)

        with search_col1:
            search_survey_id = st.text_input(
                "Search by Survey ID",
                placeholder="Enter survey ID...",
                key="search_survey_id"
            )

        with search_col2:
            search_user_id = st.text_input(
                "Search by User ID",
                placeholder="Enter user ID...",
                key="search_user_id"
            )

        # Load responses button
        if st.button("Load Responses from Local File"):
            success, result = FileManager.load_responses()
            if success:
                if "survey_responses" not in st.session_state:
                    st.session_state.survey_responses = []
                st.session_state.survey_responses.extend(result)
                st.success(f"Loaded {len(result)} responses from file")
            else:
                st.warning(result)

        # Display filtered responses
        self._display_responses(search_survey_id, search_user_id)

    def _display_responses(self, search_survey_id: str, search_user_id: str):
        """Display filtered responses"""
        if "survey_responses" in st.session_state and st.session_state.survey_responses:
            filtered_responses = st.session_state.survey_responses

            if search_survey_id:
                filtered_responses = [
                    r for r in filtered_responses
                    if search_survey_id.lower() in r.get('survey_id', '').lower()
                ]

            if search_user_id:
                filtered_responses = [
                    r for r in filtered_responses
                    if search_user_id.lower() in r.get('user_id', '').lower()
                ]

            response_obj = {
                "count": len(filtered_responses),
                "responses": filtered_responses
            }

            st.json(response_obj)

            # Download and save buttons
            col1, col2 = st.columns(2)
            with col1:
                if st.button("Download All Responses"):
                    all_json_str = json.dumps(response_obj, indent=2)
                    st.download_button(
                        label="Download All Responses as JSON",
                        data=all_json_str,
                        file_name="all_responses.json",
                        mime="application/json"
                    )

            with col2:
                if st.button("Save Responses to Local File"):
                    success, message = FileManager.save_responses(filtered_responses)
                    if success:
                        st.success(message)
                    else:
                        st.error(message)
        else:
            st.info("No responses found. Submit responses first or load from a local file.")


class SurveyInsightsTab(BaseTab):
    """Survey Insights tab implementation"""

    def __init__(self, session_manager: SessionManager, openai_client: OpenAIClient):
        super().__init__(session_manager, openai_client)
        self.survey_topics = {
            "survey_341": "Teaching Quality",
            "survey_342": "Electric Vehicle (EV) Usage",
            "survey_343": "Use of AI Assistants and Applications",
            "survey_344": "Digital Payment Platforms",
            "survey_345": "Remote Work & Hybrid Productivity"
        }

        self.example_queries = {
            "survey_341": [
                "How satisfied are students with teacher communication?",
                "What are the main concerns about curriculum effectiveness?",
                "How do teachers rate professional development opportunities?",
                "What feedback do students give about classroom technology?",
                "How effective are current student engagement strategies?"
            ],
            "survey_342": [
                "What are users' main concerns about electric vehicle charging?",
                "How satisfied are EV owners with battery performance?",
                "What factors influence electric vehicle purchase decisions?",
                "How do users rate the availability of charging infrastructure?",
                "What are the biggest barriers to EV adoption?"
            ],
            "survey_343": [
                "How do users rate AI tool productivity benefits?",
                "What are the main concerns about AI tool reliability?",
                "How satisfied are users with AI-generated content quality?",
                "What challenges do users face when adopting AI tools?",
                "How do different age groups perceive AI tool usefulness?"
            ],
            "survey_344": [
                "How secure do users feel when making online payments?",
                "What are the most preferred online payment methods?",
                "What concerns do users have about digital payment security?",
                "How satisfied are users with mobile payment experiences?",
                "What improvements do users want in payment platforms?"
            ],
            "survey_345": [
                "How has remote work affected productivity levels?",
                "What are the biggest challenges of working from home?",
                "How satisfied are employees with remote work tools?",
                "What impact has remote work had on work-life balance?",
                "How do managers evaluate remote team performance?"
            ]
        }

    def render(self):
        st.subheader("Generate Survey Insights")
        st.markdown("""
        This tool uses AI to analyze survey responses and generate insights based on your queries.
        You can ask questions about specific demographics, regions, or topics to get targeted insights.
        """)

        self._display_survey_topics()
        self._render_model_selection()
        self._render_insight_tabs()

    def _display_survey_topics(self):
        """Display available survey topics"""
        st.markdown("**Available Survey IDs and Topics:**")

        col1, col2 = st.columns(2)
        topics_list = list(self.survey_topics.items())
        mid = (len(topics_list) + 1) // 2

        for idx, (sid, topic) in enumerate(topics_list):
            if idx < mid:
                col1.markdown(f"- `{sid}`: {topic}")
            else:
                col2.markdown(f"- `{sid}`: {topic}")

    def _render_model_selection(self):
        """Render model selection and parameters"""
        col1, col2 = st.columns((0.3, 0.7))

        with col1:
            llm_provider = st.radio(
                "Model Selection",
                ["OpenAI", "Gemini"],
                help="Choose the AI model provider",
                key="llm_provider_selectbox",
                horizontal=True
            )

        with col2:
            with st.expander("Parameter Optimization", expanded=False):
                if llm_provider == "OpenAI":
                    self._render_openai_params()
                else:
                    self._render_gemini_params()

        return llm_provider

    def _render_openai_params(self):
        """Render OpenAI model parameters"""
        model_choice = st.selectbox(
            "OpenAI Model",
            ["gpt-4o", "gpt-4o-mini", "gpt-3.5-turbo"],
            index=0,
            help="Select OpenAI model variant",
            key="openai_model_choice"
        )

        temperature = st.slider(
            "Temperature",
            min_value=0.0,
            max_value=2.0,
            value=0.3,
            step=0.1,
            help="Higher values make output more random, lower values more deterministic",
            key="openai_temperature"
        )

        max_tokens = st.slider(
            "Max Tokens",
            min_value=500,
            max_value=4000,
            value=1500,
            step=100,
            help="Maximum length of the generated insight",
            key="openai_max_tokens"
        )

        top_p = st.slider(
            "Top P",
            min_value=0.0,
            max_value=1.0,
            value=1.0,
            step=0.1,
            help="Nucleus sampling parameter",
            key="openai_top_p"
        )

    def _render_gemini_params(self):
        """Render Gemini model parameters"""
        model_choice = st.selectbox(
            "Gemini Model",
            ["gemini-1.5-pro", "gemini-1.5-flash", "gemini-1.0-pro"],
            index=0,
            help="Select Gemini model variant",
            key="gemini_model_choice"
        )

        temperature = st.slider(
            "Temperature",
            min_value=0.0,
            max_value=1.0,
            value=0.4,
            step=0.1,
            help="Controls randomness in Gemini responses",
            key="gemini_temperature"
        )

        max_tokens = st.slider(
            "Max Output Tokens",
            min_value=100,
            max_value=2048,
            value=1024,
            step=50,
            help="Maximum number of tokens in Gemini response",
            key="gemini_max_tokens"
        )

        top_p = st.slider(
            "Top P",
            min_value=0.0,
            max_value=1.0,
            value=0.95,
            step=0.05,
            help="Nucleus sampling for Gemini",
            key="gemini_top_p"
        )

        top_k = st.slider(
            "Top K",
            min_value=1,
            max_value=100,
            value=40,
            step=1,
            help="Top-k sampling for Gemini",
            key="gemini_top_k"
        )

    def _render_insight_tabs(self):
        """Render insight generation tabs"""
        insight_tabs = st.tabs(["Generate Insights", "Survey Summary", "MongoDB Connection"])

        with insight_tabs[0]:
            self._render_generate_insights_tab()

        with insight_tabs[1]:
            self._render_survey_summary_tab()

        with insight_tabs[2]:
            self._render_mongodb_connection_tab()

    def _render_generate_insights_tab(self):
        """Render the generate insights tab"""
        with st.form("insight_generator_form"):
            insight_survey_id = st.text_input(
                "Survey ID",
                value='survey_341',
                placeholder="Enter the survey ID to analyze (e.g., survey_341)"
            )

            user_query = st.text_area(
                "Your Query",
                value='Summarize how users aged 18-25 in New York responded to questions about teaching quality.',
                placeholder="Example: Summarize how users aged 18-25 in New York responded to questions about teaching quality.",
                height=100
            )

            insight_button = st.form_submit_button("Generate Insights")

        if insight_button:
            if not insight_survey_id:
                st.error("Survey ID is required")
            elif not user_query:
                st.error("Query is required")
            else:
                self._process_insight_generation(insight_survey_id, user_query)

    def _process_insight_generation(self, survey_id: str, query: str):
        """Process insight generation request"""
        with st.spinner("Analyzing survey responses..."):
            try:
                # Import the SurveyInsightGenerator
                from app.api.insights import SurveyInsightGenerator

                collection_name = Config.SURVEY_COLLECTIONS.get(survey_id, Config.DEFAULT_COLLECTION)

                generator = SurveyInsightGenerator(
                    mongo_uri=os.environ.get("MONGO_URI"),
                    db_name=os.environ.get("MONGODB_DB_NAME", "survey_dev_db"),
                    collection_name=collection_name,
                    openai_api_key=os.environ.get("OPENAI_API_KEY")
                )

                start_time = time.time()
                result = generator.generate_insight(survey_id, query)
                end_time = time.time()
                execution_time = round(end_time - start_time, 2)

                self._display_insight_results(result, execution_time, survey_id)

            except ImportError:
                st.error("SurveyInsightGenerator not found. Please ensure the insights module is available.")
            except Exception as e:
                st.error(f"Error generating insights: {str(e)}")

    def _display_insight_results(self, result: dict, execution_time: float, survey_id: str):
        """Display insight generation results"""
        insight_text = result.get("insight", "").strip()
        filtered_count = result.get("filtered_count", 0)
        total_count = result.get("total_count", 0)

        if not insight_text:
            st.error(f"❌ No insight generated (took {execution_time}s)")
            st.warning("🔍 **No Results Found**")
            st.write("**Possible reasons:**")
            st.write("• No survey data found for the specified survey ID")
            st.write("• Query filters didn't match any records")
            st.write("• Database connection issues")

        elif filtered_count == 0:
            st.warning(f"⚠️ No matching records found (took {execution_time}s)")
            st.warning("🔍 **No Results Found**")
            st.write("**Your query didn't match any survey responses:**")
            st.write("• Try broadening your search criteria")
            st.write("• Check if the survey ID is correct")
            st.write("• Verify age ranges and location filters")

        elif insight_text.startswith("NO RELEVANT DATA:"):
            st.warning(f"⚠️ Query issue detected by AI (took {execution_time}s)")
            st.warning("🔍 **Query Not Specific Enough**")
            st.write("**The AI detected an issue with your query:**")
            st.write("• Your query may be too generic or not specific enough")
            st.write("• Try queries like: 'How do students rate teaching quality?' or 'What are the main concerns about curriculum?'")

            with st.expander("💡 Example Queries for This Survey", expanded=True):
                examples = self.example_queries.get(survey_id, [
                    "How satisfied are users with the service quality?",
                    "What are the main areas for improvement?",
                    "How do different demographics rate their experience?"
                ])
                for example in examples:
                    st.write(f"• {example}")

        else:
            st.success(f"✅ Insight generated successfully! (took {execution_time}s)")

            # Display metrics
            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("📊 Records Analyzed", filtered_count)
            with col2:
                st.metric("🗃️ Total Records", total_count)
            with col3:
                st.metric("⏱️ Processing Time", f"{execution_time}s")

            # Display the insight
            st.subheader("📋 Generated Insight")
            st.markdown(insight_text)

            # Display as JSON
            with st.expander("📄 View Raw Response", expanded=False):
                st.json(result)

    def _render_survey_summary_tab(self):
        """Render survey summary tab"""
        st.info("📊 Survey Summary functionality")
        st.markdown("This feature provides an overview of survey statistics and key metrics.")

        # Placeholder for survey summary functionality
        st.markdown("**Available Features:**")
        st.write("• Total response counts by survey")
        st.write("• Demographic breakdowns")
        st.write("• Response completion rates")
        st.write("• Geographic distribution")

        st.info("💡 This feature can be extended to show comprehensive survey analytics.")

    def _render_mongodb_connection_tab(self):
        """Render MongoDB connection tab"""
        st.info("🔗 MongoDB Connection Status")

        mongo_uri = os.environ.get("MONGO_URI")
        db_name = os.environ.get("MONGODB_DB_NAME", "survey_dev_db")

        if mongo_uri:
            st.success("✅ MongoDB URI configured")
            st.info(f"📊 Database: {db_name}")

            # Test connection button
            if st.button("Test MongoDB Connection"):
                self._test_mongodb_connection(mongo_uri, db_name)
        else:
            st.error("❌ MongoDB URI not configured")
            st.write("Please set the MONGO_URI environment variable.")

    def _test_mongodb_connection(self, mongo_uri: str, db_name: str):
        """Test MongoDB connection"""
        try:
            import pymongo

            with st.spinner("Testing MongoDB connection..."):
                client = pymongo.MongoClient(mongo_uri)
                db = client[db_name]

                # Test connection
                client.admin.command('ping')

                # Get collection info
                collections = db.list_collection_names()

                st.success("✅ MongoDB connection successful!")
                st.info(f"📊 Found {len(collections)} collections")

                if collections:
                    st.write("**Available Collections:**")
                    for collection in collections:
                        count = db[collection].count_documents({})
                        st.write(f"• {collection}: {count} documents")

        except ImportError:
            st.error("❌ pymongo not installed. Run: pip install pymongo")
        except Exception as e:
            st.error(f"❌ MongoDB connection failed: {str(e)}")


class SurveyInsightApp:
    """Main application class"""

    def __init__(self):
        self._setup_page_config()
        self._initialize_components()
        self._setup_sidebar()

    def _setup_page_config(self):
        """Setup Streamlit page configuration"""
        st.set_page_config(
            page_title="Survey Insight System",
            page_icon="📊",
            layout="wide"
        )

    def _initialize_components(self):
        """Initialize application components"""
        api_key = os.environ.get("OPENAI_API_KEY")
        if not api_key:
            st.error("No OpenAI API key found. Please set the OPENAI_API_KEY environment variable.")
            st.stop()

        self.openai_client = OpenAIClient(api_key)
        self.session_manager = SessionManager()

        # Initialize tabs
        self.question_tab = QuestionGeneratorTab(self.session_manager, self.openai_client)
        self.response_tab = ResponseGeneratorTab(self.session_manager, self.openai_client)
        self.view_tab = ViewResponsesTab(self.session_manager, self.openai_client)
        self.insights_tab = SurveyInsightsTab(self.session_manager, self.openai_client)

    def _setup_sidebar(self):
        """Setup the sidebar"""
        with st.sidebar:
            st.header("About")
            st.markdown("""
            This is an Object-Oriented Survey Insight System using GPT-4.

            It allows you to generate survey questions based on context and language,
            with session management for continued conversations.
            """)

            st.header("Environment")
            api_key = os.environ.get("OPENAI_API_KEY")
            if api_key:
                st.success("OpenAI API key is configured")
            else:
                st.error("OpenAI API key is missing")

            st.header("Model")
            st.info("Using GPT-4 for question generation")

    def run(self):
        """Run the main application"""
        st.title("Survey Insight System (OOP Version)")

        # Initialize main tab state
        if "active_main_tab" not in st.session_state:
            st.session_state.active_main_tab = "UI Interface"
            self.session_manager.clear_displayed_questions()

        # Main tab selection between UI and API
        main_selected_tab = st.radio(
            "Select Interface:",
            ["UI Interface", "API Documentation"],
            horizontal=True,
            key="main_tab_selector",
            label_visibility="collapsed"
        )

        # Check if main tab has changed
        if main_selected_tab != st.session_state.active_main_tab:
            st.session_state.active_main_tab = main_selected_tab
            self.session_manager.clear_displayed_questions()

        # Render selected main tab
        if main_selected_tab == "UI Interface":
            self._render_ui_interface()
        elif main_selected_tab == "API Documentation":
            self._render_api_documentation()

    def _render_ui_interface(self):
        """Render the UI interface with all tabs"""
        # Initialize active UI tab state
        if "active_ui_tab" not in st.session_state:
            st.session_state.active_ui_tab = "Question Generator"
            self.session_manager.clear_displayed_questions()

        # UI Tab selection
        ui_selected_tab = st.radio(
            "Select Option:",
            ["Question Generator", "Response Generator", "View Stored Responses", "Survey Insights"],
            horizontal=True,
            key="ui_tab_selector"
        )

        # Check if UI tab has changed
        if ui_selected_tab != st.session_state.active_ui_tab:
            st.session_state.active_ui_tab = ui_selected_tab
            self.session_manager.clear_displayed_questions()

        # Render selected UI tab
        if ui_selected_tab == "Question Generator":
            self.question_tab.render()
        elif ui_selected_tab == "Response Generator":
            self.response_tab.render()
        elif ui_selected_tab == "View Stored Responses":
            self.view_tab.render()
        elif ui_selected_tab == "Survey Insights":
            self.insights_tab.render()

    def _render_api_documentation(self):
        """Render API documentation and testing interface"""
        st.header("API Documentation & Testing")

        st.markdown("""
        This section provides comprehensive API documentation and testing capabilities for the Survey Insight System.
        The FastAPI backend provides RESTful endpoints for all functionality.
        """)

        # API Status
        self._render_api_status()

        # API Endpoints Documentation
        api_tabs = st.tabs([
            "📝 Question Generator API",
            "📋 Response Generator API",
            "📊 Insights API",
            "🔧 API Testing"
        ])

        with api_tabs[0]:
            self._render_question_api_docs()

        with api_tabs[1]:
            self._render_response_api_docs()

        with api_tabs[2]:
            self._render_insights_api_docs()

        with api_tabs[3]:
            self._render_api_testing()

    def _render_api_status(self):
        """Render API status information"""
        st.subheader("🔗 API Status")

        col1, col2, col3 = st.columns(3)

        with col1:
            st.metric("🌐 FastAPI Server", "http://localhost:8000")

        with col2:
            st.metric("📖 API Docs", "http://localhost:8000/docs")

        with col3:
            st.metric("🔑 Authentication", "API Key Required")

        st.info("**API Key:** `survey-insight-system-token-2025`")
        st.warning("⚠️ Include the API key in the `X-API-Key` header for all requests.")

    def _render_question_api_docs(self):
        """Render Question Generator API documentation"""
        st.subheader("📝 Question Generator API")

        st.markdown("### Generate Survey Questions")
        st.markdown("**Endpoint:** `POST /generate/`")

        # Request example
        st.markdown("#### Request Example")
        request_example = {
            "context": "Create a customer satisfaction survey for a software product",
            "language": "English",
            "num_questions": 5,
            "session_id": "session-12345678"
        }

        st.code(f"""
curl -X POST "http://localhost:8000/generate/" \\
  -H "Content-Type: application/json" \\
  -H "X-API-Key: survey-insight-system-token-2025" \\
  -d '{json.dumps(request_example, indent=2)}'
        """, language="bash")

        # Response example
        st.markdown("#### Response Example")
        response_example = {
            "session_id": "session-12345678",
            "questions": [
                "How satisfied are you with the overall software performance?",
                "How would you rate the user interface design?",
                "How likely are you to recommend this software to others?",
                "What features do you find most valuable?",
                "How responsive is our customer support?"
            ],
            "is_continued_session": False,
            "metadata": {
                "language": "English",
                "num_questions": 5,
                "model": "gpt-4"
            }
        }

        st.json(response_example)

    def _render_response_api_docs(self):
        """Render Response Generator API documentation"""
        st.subheader("📋 Response Generator API")

        st.markdown("### Submit Survey Response")
        st.markdown("**Endpoint:** `POST /responses/submit`")

        # Request example
        st.markdown("#### Request Example")
        request_example = {
            "survey_id": "session-12345678",
            "user_id": "user-987654",
            "age": 28,
            "region": "New York",
            "answers": [
                {
                    "question": "How satisfied are you with the overall software performance?",
                    "answer": "Very satisfied, the software runs smoothly and efficiently."
                },
                {
                    "question": "How would you rate the user interface design?",
                    "answer": "The interface is intuitive and well-designed."
                }
            ]
        }

        st.code(f"""
curl -X POST "http://localhost:8000/responses/submit" \\
  -H "Content-Type: application/json" \\
  -H "X-API-Key: survey-insight-system-token-2025" \\
  -d '{json.dumps(request_example, indent=2)}'
        """, language="bash")

        # Response example
        st.markdown("#### Response Example")
        response_example = {
            "response_id": "response-abc123",
            "survey_id": "session-12345678",
            "user_id": "user-987654",
            "status": "submitted",
            "created_at": "2025-01-27T10:30:00Z",
            "answers_count": 2
        }

        st.json(response_example)

        st.markdown("### Get Survey Responses")
        st.markdown("**Endpoint:** `GET /responses/{survey_id}`")

        st.code("""
curl -X GET "http://localhost:8000/responses/session-12345678" \\
  -H "X-API-Key: survey-insight-system-token-2025"
        """, language="bash")

    def _render_insights_api_docs(self):
        """Render Insights API documentation"""
        st.subheader("📊 Insights API")

        st.markdown("### Generate Survey Insights")
        st.markdown("**Endpoint:** `POST /insights/generate`")

        # Request example
        st.markdown("#### Request Example")
        request_example = {
            "survey_id": "survey_341",
            "user_query": "Summarize how users aged 18-25 in New York responded to questions about teaching quality.",
            "llm_provider": "OpenAI",
            "model_params": {
                "model": "gpt-4o",
                "temperature": 0.3,
                "max_tokens": 1500
            }
        }

        st.code(f"""
curl -X POST "http://localhost:8000/insights/generate" \\
  -H "Content-Type: application/json" \\
  -H "X-API-Key: survey-insight-system-token-2025" \\
  -d '{json.dumps(request_example, indent=2)}'
        """, language="bash")

        # Response example
        st.markdown("#### Response Example")
        response_example = {
            "insight": "Based on the analysis of survey responses from users aged 18-25 in New York regarding teaching quality, several key findings emerge...",
            "filtered_count": 45,
            "total_count": 1250,
            "execution_time": 3.2,
            "survey_id": "survey_341",
            "query": "Summarize how users aged 18-25 in New York responded to questions about teaching quality.",
            "metadata": {
                "model": "gpt-4o",
                "provider": "OpenAI",
                "collection": "teaching_quality"
            }
        }

        st.json(response_example)

    def _render_api_testing(self):
        """Render API testing interface"""
        st.subheader("🔧 API Testing Interface")

        st.markdown("Test the API endpoints directly from this interface.")

        # API endpoint selection
        endpoint = st.selectbox(
            "Select API Endpoint",
            [
                "POST /generate/ - Generate Questions",
                "POST /responses/submit - Submit Response",
                "GET /responses/{survey_id} - Get Responses",
                "POST /insights/generate - Generate Insights"
            ]
        )

        if "Generate Questions" in endpoint:
            self._render_question_api_test()
        elif "Submit Response" in endpoint:
            self._render_response_api_test()
        elif "Get Responses" in endpoint:
            self._render_get_responses_test()
        elif "Generate Insights" in endpoint:
            self._render_insights_api_test()

    def _render_question_api_test(self):
        """Render question generation API test"""
        st.markdown("#### Test Question Generation API")

        with st.form("question_api_test"):
            context = st.text_area(
                "Context",
                value="Create a customer satisfaction survey for a software product",
                height=100
            )

            col1, col2 = st.columns(2)
            with col1:
                language = st.selectbox("Language", Config.LANGUAGES)
            with col2:
                num_questions = st.number_input("Number of Questions", min_value=1, max_value=20, value=5)

            session_id = st.text_input("Session ID (optional)", placeholder="session-12345678")

            if st.form_submit_button("Test API"):
                self._execute_question_api_test(context, language, num_questions, session_id)

    def _execute_question_api_test(self, context: str, language: str, num_questions: int, session_id: str):
        """Execute question generation API test"""
        import requests

        url = "http://localhost:8000/generate/"
        headers = {
            "Content-Type": "application/json",
            "X-API-Key": "survey-insight-system-token-2025"
        }

        payload = {
            "context": context,
            "language": language,
            "num_questions": num_questions
        }

        if session_id:
            payload["session_id"] = session_id

        try:
            with st.spinner("Testing API..."):
                response = requests.post(url, json=payload, headers=headers, timeout=30)

            if response.status_code == 200:
                st.success("✅ API call successful!")
                st.json(response.json())
            else:
                st.error(f"❌ API call failed with status {response.status_code}")
                st.code(response.text)

        except requests.exceptions.ConnectionError:
            st.error("❌ Cannot connect to FastAPI server. Make sure it's running on http://localhost:8000")
        except Exception as e:
            st.error(f"❌ Error: {str(e)}")

    def _render_response_api_test(self):
        """Render response submission API test"""
        st.markdown("#### Test Response Submission API")

        with st.form("response_api_test"):
            survey_id = st.text_input("Survey ID", value="session-12345678")
            user_id = st.text_input("User ID", value="user-987654")

            col1, col2 = st.columns(2)
            with col1:
                age = st.number_input("Age", min_value=0, max_value=120, value=28)
            with col2:
                region = st.text_input("Region", value="New York")

            st.markdown("**Answers:**")
            question1 = st.text_input("Question 1", value="How satisfied are you with the software?")
            answer1 = st.text_area("Answer 1", value="Very satisfied with the performance.")

            question2 = st.text_input("Question 2", value="How would you rate the user interface?")
            answer2 = st.text_area("Answer 2", value="The interface is intuitive and well-designed.")

            if st.form_submit_button("Test API"):
                answers = []
                if question1 and answer1:
                    answers.append({"question": question1, "answer": answer1})
                if question2 and answer2:
                    answers.append({"question": question2, "answer": answer2})

                self._execute_response_api_test(survey_id, user_id, age, region, answers)

    def _execute_response_api_test(self, survey_id: str, user_id: str, age: int, region: str, answers: list):
        """Execute response submission API test"""
        import requests

        url = "http://localhost:8000/responses/submit"
        headers = {
            "Content-Type": "application/json",
            "X-API-Key": "survey-insight-system-token-2025"
        }

        payload = {
            "survey_id": survey_id,
            "user_id": user_id,
            "age": age if age > 0 else None,
            "region": region if region else None,
            "answers": answers
        }

        try:
            with st.spinner("Testing API..."):
                response = requests.post(url, json=payload, headers=headers, timeout=30)

            if response.status_code == 200:
                st.success("✅ API call successful!")
                st.json(response.json())
            else:
                st.error(f"❌ API call failed with status {response.status_code}")
                st.code(response.text)

        except requests.exceptions.ConnectionError:
            st.error("❌ Cannot connect to FastAPI server. Make sure it's running on http://localhost:8000")
        except Exception as e:
            st.error(f"❌ Error: {str(e)}")

    def _render_get_responses_test(self):
        """Render get responses API test"""
        st.markdown("#### Test Get Responses API")

        with st.form("get_responses_test"):
            survey_id = st.text_input("Survey ID", value="session-12345678")

            if st.form_submit_button("Test API"):
                self._execute_get_responses_test(survey_id)

    def _execute_get_responses_test(self, survey_id: str):
        """Execute get responses API test"""
        import requests

        url = f"http://localhost:8000/responses/{survey_id}"
        headers = {
            "X-API-Key": "survey-insight-system-token-2025"
        }

        try:
            with st.spinner("Testing API..."):
                response = requests.get(url, headers=headers, timeout=30)

            if response.status_code == 200:
                st.success("✅ API call successful!")
                st.json(response.json())
            else:
                st.error(f"❌ API call failed with status {response.status_code}")
                st.code(response.text)

        except requests.exceptions.ConnectionError:
            st.error("❌ Cannot connect to FastAPI server. Make sure it's running on http://localhost:8000")
        except Exception as e:
            st.error(f"❌ Error: {str(e)}")

    def _render_insights_api_test(self):
        """Render insights API test"""
        st.markdown("#### Test Insights Generation API")

        with st.form("insights_api_test"):
            survey_id = st.text_input("Survey ID", value="survey_341")
            user_query = st.text_area(
                "Query",
                value="Summarize how users aged 18-25 in New York responded to questions about teaching quality.",
                height=100
            )

            col1, col2 = st.columns(2)
            with col1:
                llm_provider = st.selectbox("LLM Provider", ["OpenAI", "Gemini"])
            with col2:
                model = st.selectbox(
                    "Model",
                    ["gpt-4o", "gpt-4o-mini", "gpt-3.5-turbo"] if llm_provider == "OpenAI"
                    else ["gemini-1.5-pro", "gemini-1.5-flash"]
                )

            if st.form_submit_button("Test API"):
                self._execute_insights_api_test(survey_id, user_query, llm_provider, model)

    def _execute_insights_api_test(self, survey_id: str, user_query: str, llm_provider: str, model: str):
        """Execute insights API test"""
        import requests

        url = "http://localhost:8000/insights/generate"
        headers = {
            "Content-Type": "application/json",
            "X-API-Key": "survey-insight-system-token-2025"
        }

        payload = {
            "survey_id": survey_id,
            "user_query": user_query,
            "llm_provider": llm_provider,
            "model_params": {
                "model": model,
                "temperature": 0.3,
                "max_tokens": 1500
            }
        }

        try:
            with st.spinner("Testing API..."):
                response = requests.post(url, json=payload, headers=headers, timeout=60)

            if response.status_code == 200:
                st.success("✅ API call successful!")
                st.json(response.json())
            else:
                st.error(f"❌ API call failed with status {response.status_code}")
                st.code(response.text)

        except requests.exceptions.ConnectionError:
            st.error("❌ Cannot connect to FastAPI server. Make sure it's running on http://localhost:8000")
        except Exception as e:
            st.error(f"❌ Error: {str(e)}")


def main():
    """Main entry point"""
    app = SurveyInsightApp()
    app.run()


if __name__ == "__main__":
    main()
