"""
Survey Insight Generator

This script connects to a MongoDB database, retrieves survey responses based on a survey_id,
and generates insights using OpenAI based on user queries.

Usage:
    python survey_insight_generator.py --survey_id "survey_341" --query "Summarize how users aged 18-25 in New York responded to questions about teaching quality."

Or import and use the SurveyInsightGenerator class directly in your code.
"""

import os
import json
import argparse
import logging
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
import re
import streamlit as st

# Setup logging for this module
logger = logging.getLogger(__name__)


# MongoDB imports
from pymongo import MongoClient
from pymongo.collection import Collection
from bson import ObjectId

# OpenAI imports
import openai
import time
# Load environment variables (if using .env file)
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

class MongoJSONEncoder(json.JSONEncoder):
    """Custom JSON encoder for MongoDB objects like ObjectId."""
    def default(self, obj):
        if isinstance(obj, ObjectId):
            return str(obj)
        if isinstance(obj, datetime):
            return obj.isoformat()
        return super().default(obj)

class SurveyInsightGenerator:
    """
    A class to generate insights from survey responses stored in MongoDB
    using OpenAI's language models.
    """

    def __init__(self,
                 mongo_uri: Optional[str] = None,
                 db_name: str = "survey_dev_db",
                 collection_name: str = "teaching_quality",
                 openai_api_key: Optional[str] = None,
                 llm_provider: str = "OpenAI",
                 model_name: str = "gpt-4o"):
        """
        Initialize the SurveyInsightGenerator.

        Args:
            mongo_uri: MongoDB connection URI. If None, uses MONGODB_URI env variable.
            db_name: Name of the MongoDB database.
            collection_name: Name of the collection containing survey responses.
            openai_api_key: OpenAI API key. If None, uses OPENAI_API_KEY env variable.
            llm_provider: LLM provider to use ("OpenAI" or "Gemini").
            model_name: Model name to use for the selected provider.
        """
        # Set up MongoDB connection
        self.mongo_uri = mongo_uri or os.environ.get("MONGO_URI")
        if not self.mongo_uri:
            raise ValueError("MongoDB URI not provided and MONGODB_URI environment variable not set")

        logger.info(f"Initializing SurveyInsightGenerator - DB: {db_name}, Collection: {collection_name}, LLM: {llm_provider}, Model: {model_name}")

        self.client = MongoClient(self.mongo_uri)
        self.db = self.client[db_name]
        self.collection = self.db[collection_name]

        # Set up LLM configuration
        self.llm_provider = llm_provider
        self.model_name = model_name

        if llm_provider.lower() == "gemini":
            try:
                import google.generativeai as genai
                genai.configure(api_key=os.getenv("GOOGLE_API_KEY"))
            except ImportError:
                raise ImportError("Google Generative AI library not installed. Please install it to use Gemini.")
            except Exception as e:
                raise ValueError(f"Failed to configure Google Generative AI: {str(e)}")

        elif llm_provider.lower() == "openai":
            # Check if OpenAI API key is provided
            self.openai_api_key = openai_api_key or os.environ.get("OPENAI_API_KEY")
            if not self.openai_api_key:
                raise ValueError("OpenAI API key not provided and OPENAI_API_KEY environment variable not set")

            # Set the API key for the OpenAI module
            openai.api_key = self.openai_api_key

    def get_survey_responses(self, survey_id: str) -> List[Dict[str, Any]]:
        """
        Retrieve all responses for a specific survey.

        Args:
            survey_id: The ID of the survey to retrieve responses for.

        Returns:
            A list of response documents.
        """
        cursor = self.collection.find({"survey_id": survey_id})
        return list(cursor)

    def filter_responses(self,
                         responses: List[Dict[str, Any]],
                         filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Filter responses based on criteria like age range, region, gender, education, etc.

        Args:
            responses: List of response documents.
            filters: Dictionary of filter criteria.

        Returns:
            Filtered list of response documents.
        """
        # If no filters, return all responses
        if not filters:
            return responses

        filtered_responses = responses

        # Filter by age range
        if 'age_min' in filters and 'age_max' in filters:
            filtered_responses = [
                r for r in filtered_responses
                if r.get('age') is not None and
                filters['age_min'] <= r.get('age', 0) <= filters['age_max']
            ]

        # Filter by region - use fuzzy matching for better results
        if 'region' in filters:
            region_pattern = re.compile(filters['region'], re.IGNORECASE)
            filtered_responses = [
                r for r in filtered_responses
                if r.get('region') and region_pattern.search(r.get('region', ''))
            ]

        # Filter by gender
        if 'gender' in filters:
            gender_pattern = re.compile(filters['gender'], re.IGNORECASE)
            filtered_responses = [
                r for r in filtered_responses
                if r.get('gender') and gender_pattern.search(r.get('gender', ''))
            ]

        # Filter by education level
        if 'education' in filters:
            education_pattern = re.compile(filters['education'], re.IGNORECASE)
            filtered_responses = [
                r for r in filtered_responses
                if r.get('education') and education_pattern.search(r.get('education', ''))
            ]

        # Filter by income level - this is more complex and might need parsing
        if 'income' in filters:
            # Simple string matching for now
            income_pattern = re.compile(filters['income'], re.IGNORECASE)
            filtered_responses = [
                r for r in filtered_responses
                if r.get('income') and income_pattern.search(str(r.get('income', '')))
            ]

        # Filter by user_id
        if 'user_id' in filters:
            filtered_responses = [
                r for r in filtered_responses
                if r.get('user_id') == filters['user_id']
            ]

        # Log the filtering results
        print(f"Applied filters: {filters}")
        print(f"Filtered from {len(responses)} to {len(filtered_responses)} responses")

        return filtered_responses

    # def extract_filters_from_query(self, query: str) -> Dict[str, Any]:
    #     """
    #     Extract filter criteria from a natural language query.

    #     Args:
    #         query: Natural language query string.

    #     Returns:
    #         Dictionary of extracted filters.
    #     """
    #     filters = {}

    #     # Extract age range - multiple patterns
    #     # Pattern 1: "aged X-Y" or "aged X to Y"
    #     age_range_match = re.search(r'aged\s+(\d+)(?:\s*[-–to]\s*(\d+))?', query, re.IGNORECASE)
    #     if age_range_match:
    #         min_age = int(age_range_match.group(1))
    #         max_age = int(age_range_match.group(2)) if age_range_match.group(2) else min_age
    #         filters['age_min'] = min_age
    #         filters['age_max'] = max_age

    #     # Pattern 2: "between X and Y years old" or "between ages X and Y"
    #     age_between_match = re.search(r'between\s+(?:ages\s+)?(\d+)\s+and\s+(\d+)(?:\s+years\s+old)?', query, re.IGNORECASE)
    #     if age_between_match and 'age_min' not in filters:
    #         min_age = int(age_between_match.group(1))
    #         max_age = int(age_between_match.group(2))
    #         filters['age_min'] = min_age
    #         filters['age_max'] = max_age

    #     # Pattern 3: "under X years old" or "younger than X"
    #     age_under_match = re.search(r'(?:under|younger\s+than)\s+(\d+)(?:\s+years\s+old)?', query, re.IGNORECASE)
    #     if age_under_match and 'age_min' not in filters:
    #         filters['age_max'] = int(age_under_match.group(1))
    #         filters['age_min'] = 0

    #     # Pattern 4: "over X years old" or "older than X"
    #     age_over_match = re.search(r'(?:over|older\s+than)\s+(\d+)(?:\s+years\s+old)?', query, re.IGNORECASE)
    #     if age_over_match and 'age_max' not in filters:
    #         filters['age_min'] = int(age_over_match.group(1))
    #         filters['age_max'] = 120  # Reasonable upper limit

    #     # Pattern 5: "X year olds" or "X-year-olds"
    #     age_specific_match = re.search(r'(\d+)[\s-]year[\s-]olds?', query, re.IGNORECASE)
    #     if age_specific_match and 'age_min' not in filters:
    #         age = int(age_specific_match.group(1))
    #         filters['age_min'] = age
    #         filters['age_max'] = age

    #     # Extract region/location - multiple patterns
    #     # Pattern 1: "in X" (city, state, country)
    #     region_match = re.search(r'in\s+([A-Za-z\s,]+?)(?:\s+(?:region|area|responded|who|that|with|and|or|$))', query, re.IGNORECASE)
    #     if region_match:
    #         filters['region'] = region_match.group(1).strip()

    #     # Pattern 2: "from X" (city, state, country)
    #     from_match = re.search(r'from\s+([A-Za-z\s,]+?)(?:\s+(?:region|area|responded|who|that|with|and|or|$))', query, re.IGNORECASE)
    #     if from_match and 'region' not in filters:
    #         filters['region'] = from_match.group(1).strip()

    #     # Pattern 3: "living in X" or "located in X"
    #     living_match = re.search(r'(?:living|located)\s+in\s+([A-Za-z\s,]+?)(?:\s+(?:region|area|responded|who|that|with|and|or|$))', query, re.IGNORECASE)
    #     if living_match and 'region' not in filters:
    #         filters['region'] = living_match.group(1).strip()

    #     # Extract gender if mentioned
    #     gender_match = re.search(r'\b(males?|females?|men|women|boys|girls)\b', query, re.IGNORECASE)
    #     if gender_match:
    #         gender = gender_match.group(1).lower()
    #         if gender in ['male', 'males', 'men', 'boys']:
    #             filters['gender'] = 'male'
    #         elif gender in ['female', 'females', 'women', 'girls']:
    #             filters['gender'] = 'female'

    #     # Extract education level if mentioned
    #     education_patterns = [
    #         (r'\b(?:high\s+school|secondary\s+education)\b', 'high school'),
    #         (r'\b(?:college|undergraduate|bachelor\'?s?)\b', 'college'),
    #         (r'\b(?:graduate|master\'?s?|phd|doctorate)\b', 'graduate'),
    #         (r'\b(?:primary\s+education|elementary\s+school)\b', 'elementary')
    #     ]

    #     for pattern, level in education_patterns:
    #         if re.search(pattern, query, re.IGNORECASE):
    #             filters['education'] = level
    #             break

    #     # Extract income level if mentioned
    #     income_match = re.search(r'(?:income|earning)(?:\s+level)?\s+(?:of|above|below|between)?\s+\$?(\d+[k,]?(?:\s*-\s*\$?\d+[k,]?)?)', query, re.IGNORECASE)
    #     if income_match:
    #         filters['income'] = income_match.group(1)

    #     return filters

    def extract_filters_from_query(self, query: str) -> Dict[str, Any]:
        """
        Extract filter criteria from a natural language query.

        Args:
            query: Natural language query string.

        Returns:
            Dictionary of extracted filters.
        """
        filters = {}

        # Extract age range
        # Use LLM system prompts to extract age group and region from the query
        # For this, call OpenAI to extract structured filters from the query


        SYSTEM_PROMPT = """
        You are an expert at extracting structured filters from survey analysis queries.
        Given a user query, extract the following as a JSON object:
        - age_min: integer (minimum age if specified or implied)
        - age_max: integer (maximum age if specified or implied)
        - age_group: string (if the query mentions "young", "middle aged", "elderly", etc., map them to typical age ranges: "young"=18-25, "middle aged"=40-60, "elderly"=65+)
        - region: string (city, state, country, or region if mentioned)
        Only include keys that are present or implied in the query. If not present, omit the key.
        Examples:
        Query: "Summarize how young people in New York responded"
        Output: {"age_min": 18, "age_max": 25, "region": "New York"}
        Query: "Analyze responses from middle aged adults in California"
        Output: {"age_min": 40, "age_max": 60, "region": "California"}
        Query: "What do elderly respondents in Texas think?"
        Output: {"age_min": 65, "region": "Texas"}
        Query: "Summarize responses from users aged 18-30 in London"
        Output: {"age_min": 18, "age_max": 30, "region": "London"}
        Query: "Summarize all responses"
        Output: {}

        Now extract the filters from this query:
        {query}
        Output:
        """

        try:
            # Use OpenAI for filter extraction (keeping it simple for now)
            openai_api_key = self.openai_api_key if hasattr(self, "openai_api_key") else os.environ.get("OPENAI_API_KEY")
            openai_client = openai.OpenAI(api_key=openai_api_key)
            completion = openai_client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": SYSTEM_PROMPT},
                {"role": "user", "content": query}
            ],
            temperature=0,
            max_tokens=200
            )
            content = completion.choices[0].message.content.strip()
            # Try to extract JSON from the LLM output
            match = re.search(r'\{.*\}', content, re.DOTALL)
            if match:
                filters = json.loads(match.group(0))
            else:
                filters = {}
        except Exception as e:
            # Fallback to regex if LLM fails
            filters = {}

            # Age group keywords
            if re.search(r'\byoung\b', query, re.IGNORECASE):
                filters['age_min'] = 18
                filters['age_max'] = 25
            elif re.search(r'\bmiddle[- ]?aged\b', query, re.IGNORECASE):
                filters['age_min'] = 40
                filters['age_max'] = 60
            elif re.search(r'\belderly\b', query, re.IGNORECASE):
                filters['age_min'] = 65

            # Age range patterns
            age_range_match = re.search(r'aged\s+(\d+)(?:\s*[-–to]\s*(\d+))?', query, re.IGNORECASE)
            if age_range_match:
                filters['age_min'] = int(age_range_match.group(1))
                filters['age_max'] = int(age_range_match.group(2)) if age_range_match.group(2) else int(age_range_match.group(1))

            # Region
            region_match = re.search(r'in\s+([A-Za-z\s]+?)(?:\s+responded|$)', query, re.IGNORECASE)
            if region_match:
                filters['region'] = region_match.group(1).strip()

        return filters
    def generate_insight(self,
                         survey_id: str,
                         user_query: str,
                         llm_config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Generate insights from survey responses based on a user query.

        Args:
            survey_id: The ID of the survey to analyze.
            user_query: Natural language query describing the insights to generate.
            llm_config: Optional LLM configuration containing provider, model, temperature, etc.

        Returns:
            Dictionary containing the generated insights and metadata.
        """
        logger.info(f"Starting insight generation - Survey: {survey_id}, Query: {user_query}")

        # Get all responses for the survey
        responses = self.get_survey_responses(survey_id)
        logger.info(f"Retrieved {len(responses)} total responses for survey: {survey_id}")

        if not responses:
            logger.warning(f"No responses found for survey_id: {survey_id}")
            return {
                "survey_id": survey_id,
                "query": user_query,
                "error": f"No responses found for survey_id: {survey_id}",
                "insight": None,
                "filtered_count": 0,
                "total_count": 0
            }

        # Extract filters from the query
        filters = self.extract_filters_from_query(user_query)
        logger.info(f"Extracted filters from query: {filters}")
        # st.write('filters:', filters)

        # Apply filters to get relevant responses from MongoDB
        if filters:
            # Build MongoDB filter query dynamically based on extracted filters
            filter_query = {"survey_id": survey_id}  # Always filter by survey_id

            # Define possible field mappings for different collection structures
            field_mappings = {
                "age": ["age", "demographics.age", "user_age", "respondent_age"],
                "region": ["region", "location", "geographics.location", "user_location", "city", "state", "country"],
                # "gender": ["gender", "demographics.gender", "user_gender", "sex"],
                # "education": ["education", "education_level", "demographics.education", "user_education"],
                # "income": ["income", "income_level", "demographics.income", "user_income"],
                # "user_id": ["user_id", "_id", "respondent_id", "participant_id"]
            }

            # Add age filters if present
            if "age_min" in filters or "age_max" in filters:
                age_filter = {}
                if "age_min" in filters:
                    age_filter["$gte"] = filters["age_min"]
                if "age_max" in filters:
                    age_filter["$lte"] = filters["age_max"]

                # Try different age field names
                age_fields = field_mappings["age"]
                age_conditions = []
                for field in age_fields:
                    age_conditions.append({field: age_filter})

                if len(age_conditions) > 1:
                    filter_query["$or"] = age_conditions
                else:
                    filter_query[age_fields[0]] = age_filter

            # Add region filter if present (flexible matching)
            if "region" in filters:
                region_conditions = []
                for field in field_mappings["region"]:
                    region_conditions.append({field: {"$regex": filters["region"], "$options": "i"}})

                if "$or" in filter_query:
                    # Combine with existing $or conditions
                    filter_query["$and"] = [
                        {"$or": filter_query.pop("$or")},
                        {"$or": region_conditions}
                    ]
                else:
                    filter_query["$or"] = region_conditions

            # st.write('filter_query:', filter_query)
            # # Add gender filter if present
            # if "gender" in filters:
            #     gender_conditions = []
            #     for field in field_mappings["gender"]:
            #         gender_conditions.append({field: {"$regex": filters["gender"], "$options": "i"}})

            #     if "$or" in filter_query or "$and" in filter_query:
            #         # Add to existing complex query
            #         existing_conditions = []
            #         if "$and" in filter_query:
            #             existing_conditions = filter_query["$and"]
            #         elif "$or" in filter_query:
            #             existing_conditions = [{"$or": filter_query.pop("$or")}]

            #         existing_conditions.append({"$or": gender_conditions})
            #         filter_query["$and"] = existing_conditions
            #     else:
            #         filter_query["$or"] = gender_conditions

            # # Add education filter if present
            # if "education" in filters:
            #     education_conditions = []
            #     for field in field_mappings["education"]:
            #         education_conditions.append({field: {"$regex": filters["education"], "$options": "i"}})

            #     if "$and" in filter_query:
            #         filter_query["$and"].append({"$or": education_conditions})
            #     elif "$or" in filter_query:
            #         filter_query["$and"] = [
            #             {"$or": filter_query.pop("$or")},
            #             {"$or": education_conditions}
            #         ]
            #     else:
            #         filter_query["$or"] = education_conditions

            # # Add income filter if present
            # if "income" in filters:
            #     income_conditions = []
            #     for field in field_mappings["income"]:
            #         income_conditions.append({field: {"$regex": str(filters["income"]), "$options": "i"}})

            #     if "$and" in filter_query:
            #         filter_query["$and"].append({"$or": income_conditions})
            #     elif "$or" in filter_query:
            #         filter_query["$and"] = [
            #             {"$or": filter_query.pop("$or")},
            #             {"$or": income_conditions}
            #         ]
            #     else:
            #         filter_query["$or"] = income_conditions

            # # Add user_id filter if present (exact match)
            # if "user_id" in filters:
            #     user_id_conditions = []
            #     for field in field_mappings["user_id"]:
            #         user_id_conditions.append({field: filters["user_id"]})

            #     if "$and" in filter_query:
            #         filter_query["$and"].append({"$or": user_id_conditions})
            #     elif "$or" in filter_query:
            #         filter_query["$and"] = [
            #             {"$or": filter_query.pop("$or")},
            #             {"$or": user_id_conditions}
            #         ]
            #     else:
            #         filter_query["$or"] = user_id_conditions

            # Query the collection with dynamic filters
            try:
                logger.info(f"Applying MongoDB filter query: {filter_query}")
                filtered_responses = list(self.collection.find(filter_query))
                logger.info(f"MongoDB filtering completed - Found {len(filtered_responses)} responses matching filters")
                # if hasattr(st, 'write'):
                #     st.write(f"📊 Applied filters: {filters}")
                #     st.write(f"🔍 MongoDB query: {filter_query}")
                #     st.write(f"✅ Found {len(filtered_responses)} responses matching filters")
            except Exception as e:
                logger.error(f"Error applying MongoDB filters: {str(e)}. Using all responses.")
                if hasattr(st, 'warning'):
                    st.warning(f"Error applying filters: {str(e)}. Using all responses.")
                filtered_responses = responses
        else:
            # No filters to apply, use all responses
            logger.info("No filters extracted, using all responses")
            filtered_responses = responses

        # Check if we have any responses after filtering
        if len(filtered_responses) == 0:
            # If no responses match the filters, use all responses instead
            logger.warning(f"No responses match the filters. Using all {len(responses)} responses instead.")
            if hasattr(st, 'write'):
                st.write(f"⚠️ No responses match the filters. Using all {len(responses)} responses instead.")
            filtered_responses = responses

            # Create a warning message to include in the prompt
            warning_message = "Note: No responses matched the specific filters you mentioned. This analysis uses all available responses."
        else:
            warning_message = ""


        # Prepare data for OpenAI
        response_data = json.dumps(filtered_responses, cls=MongoJSONEncoder)
        # st.write("response_data:", response_data)

        # Generate insights using OpenAI
        SYSTEM_PROMPT = f"""
        You are an expert data analyst specializing in survey analysis.

        INSTRUCTIONS:

        - The user may ask for any type of analysis related to the survey data, including but not limited to: summary, statistics, sentiment analysis, trends, outliers, or any other valid analytical request.
        - If the user query is valid and related to the survey, provide a response strictly according to what the user asks. Do not add extra information, introductions, or conclusions.
        - Mention specific demographics (age, location, etc.)
        - Ask about specific aspects of the survey.
        - Request analysis of user responses or feedback
        - Are clearly related to the survey content

        ONLY use "NO RELEVANT DATA:" for:
        - Obvious test queries: "test", "testing", "hello", "sample", "demo"
        - Completely unrelated topics: or ncoud able to answer from the survey results.
        - When there are literally ZERO responses in the data (not just few responses)

        EXAMPLES OF VALID QUERIES (NEVER REJECT THESE):
        - "Summarize how users responded to questions about teaching quality"
        - "Analyze responses from users aged 18-25"
        - "What do people think about curriculum effectiveness"
        - "How satisfied are users with the service"
        - Any query asking for analysis, summary, or insights about survey content

        Now analyze the following survey responses and provide insights based on this query: "{user_query}"

        Survey ID: {survey_id}
        Number of responses: {len(filtered_responses)}
        Filters applied: {json.dumps(filters, indent=2)}
        {warning_message}

        Survey Responses:
        {response_data}

        REMEMBER: The query "{user_query}" appears to be a legitimate analytical request. Unless it's obviously a test query like "test" or "hello", provide a comprehensive analysis of the available data, even if the sample size is small.

        Where appropriate, consider not just add all points add according to {user_query}:
        1. A clear summary of the key findings (noting sample size if small)
        2. Specific patterns or trends you observe in the data
        3. Notable outliers or exceptions
        4. Direct answers to any specific questions in the user query
        5. Relevant statistics or percentages to support your insights
        6. Limitations due to sample size if applicable

        Format your response in markdown with clear sections and bullet points where appropriate.
        Be concise but thorough, focusing on the most relevant insights for the query.
        don't add intros and outros.
        """

        try:
            # Set default LLM configuration if not provided
            if llm_config is None:
                llm_config = {
                    "provider": self.llm_provider,
                    "model": self.model_name,
                    "temperature": 0.3,
                    "max_tokens": 2000
                }

            # Extract LLM configuration
            provider = llm_config.get("provider", "OpenAI")
            model = llm_config.get("model", "gpt-4o")
            temperature = llm_config.get("temperature", 0.3)
            max_tokens = llm_config.get("max_tokens", 2000)

            logger.info(f"Starting LLM processing - Provider: {provider}, Model: {model}, Temperature: {temperature}, Max tokens: {max_tokens}")

            # Use OpenAI for now (can be extended to support other providers)
            if provider.lower() == "openai":
                openai_client = openai.OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
                response = openai_client.chat.completions.create(
                    model=model,
                    messages=[
                        {"role": "system", "content": SYSTEM_PROMPT},
                        {"role": "user", "content": user_query}
                    ],
                    temperature=temperature,
                    max_tokens=max_tokens
                )
            elif provider.lower() == "gemini":
                # Use Google Gemini API
                try:
                    import google.generativeai as genai

                    # Configure Gemini with API key
                    genai.configure(api_key=os.getenv("GOOGLE_API_KEY"))

                    # Initialize Gemini model with safety settings
                    safety_settings = [
                        {
                            "category": "HARM_CATEGORY_HARASSMENT",
                            "threshold": "BLOCK_NONE"
                        },
                        {
                            "category": "HARM_CATEGORY_HATE_SPEECH",
                            "threshold": "BLOCK_NONE"
                        },
                        {
                            "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                            "threshold": "BLOCK_NONE"
                        },
                        {
                            "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
                            "threshold": "BLOCK_NONE"
                        }
                    ]

                    gemini_model = genai.GenerativeModel(
                        model_name=model,
                        safety_settings=safety_settings
                    )
                    # Create Gemini-optimized prompt (different from OpenAI format)
                    gemini_prompt = f"""You are an expert data analyst specializing in survey analysis.

                        TASK: Analyze the following survey responses and provide insights based on the user's query.

                        USER QUERY: "{user_query}"

                        SURVEY INFORMATION:
                        - Survey ID: {survey_id}
                        - Number of responses: {len(filtered_responses)}
                        - Filters applied: {json.dumps(filters, indent=2)}
                        {warning_message}

                        SURVEY RESPONSES DATA:
                        {response_data}

                        ANALYSIS INSTRUCTIONS:
                        1. Provide a comprehensive analysis of the survey data based on the user's query
                        2. Focus on answering the specific question asked in the user query
                        3. Include relevant statistics, patterns, and insights from the data
                        4. Use markdown formatting with clear sections and bullet points
                        5. Be concise but thorough, focusing on the most relevant insights
                        6. Do not add unnecessary introductions or conclusions

                        IMPORTANT: This is a legitimate analytical request about survey data. Provide a detailed analysis of the available responses. The query "{user_query}" is asking for insights about survey data, so analyze the provided survey responses thoroughly.

                        Where appropriate, include:
                        - Key findings summary (noting sample size if small)
                        - Specific patterns or trends observed in the data
                        - Notable outliers or exceptions
                        - Direct answers to specific questions in the user query
                        - Relevant statistics or percentages to support insights
                        - Limitations due to sample size if applicable

                        Your analysis:"""

                    # Generate response using Gemini
                    gemini_response = gemini_model.generate_content(
                        gemini_prompt,
                        generation_config=genai.types.GenerationConfig(
                            temperature=temperature,
                            max_output_tokens=max_tokens,
                        )
                    )

                    # Validate Gemini response
                    if not gemini_response or not gemini_response.text:
                        raise Exception("Gemini returned empty response")

                    # Check if Gemini response is blocked or filtered
                    if hasattr(gemini_response, 'prompt_feedback') and gemini_response.prompt_feedback:
                        if hasattr(gemini_response.prompt_feedback, 'block_reason'):
                            raise Exception(f"Gemini blocked response: {gemini_response.prompt_feedback.block_reason}")

                    # Create response object compatible with OpenAI format
                    class GeminiResponse:
                        def __init__(self, text):
                            self.choices = [type('obj', (object,), {
                                'message': type('obj', (object,), {'content': text})()
                            })()]

                    # Post-process Gemini response to handle potential issues
                    gemini_text = gemini_response.text.strip()

                    # Check if Gemini incorrectly returned "NO RELEVANT DATA" for legitimate queries
                    if gemini_text.startswith("NO RELEVANT DATA:") and len(filtered_responses) > 0:
                        # Override with a basic analysis since we have data
                        gemini_text = f"""## Survey Analysis Results

                        **Query:** {user_query}

                        **Data Summary:**
                        - Total responses analyzed: {len(filtered_responses)}
                        - Survey ID: {survey_id}

                        **Analysis:**
                        Based on the available survey responses, here are the key insights:

                        - The survey contains {len(filtered_responses)} responses for analysis
                        - Data is available for the requested query: "{user_query}"
                        - Filters applied: {json.dumps(filters, indent=2) if filters else "None"}

                        **Note:** This analysis was generated because sufficient survey data is available to answer your query. The system initially flagged this as having no relevant data, but upon review, there are {len(filtered_responses)} responses that can provide insights for your question.

                        **Recommendation:** Please try rephrasing your query to be more specific about what aspects of the survey data you'd like to analyze."""

                    response = GeminiResponse(gemini_text)

                    # Debug output (can be removed in production)
                    if hasattr(st, 'write'):
                        st.write('✅ Gemini response received and processed successfully')

                except ImportError:
                    # Fallback to OpenAI if google-generativeai is not installed
                    st.warning("Google Generative AI library not installed. Falling back to OpenAI.")
                    openai_client = openai.OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
                    response = openai_client.chat.completions.create(
                        model="gpt-4o",
                        messages=[
                            {"role": "system", "content": SYSTEM_PROMPT},
                            {"role": "user", "content": user_query}
                        ],
                        temperature=temperature,
                        max_tokens=max_tokens
                    )
                except Exception as e:
                    # Fallback to OpenAI if Gemini fails
                    st.warning(f"Gemini API failed: {str(e)}. Falling back to OpenAI.")
                    openai_client = openai.OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
                    response = openai_client.chat.completions.create(
                        model="gpt-4o",
                        messages=[
                            {"role": "system", "content": SYSTEM_PROMPT},
                            {"role": "user", "content": user_query}
                        ],
                        temperature=temperature,
                        max_tokens=max_tokens
                    )
            # else:
            #     # Default to OpenAI
            #     openai_client = openai.OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
            #     response = openai_client.chat.completions.create(
            #         model="gpt-4o",
            #         messages=[
            #             {"role": "system", "content": SYSTEM_PROMPT},
            #             {"role": "user", "content": user_query}
            #         ],
            #         temperature=temperature,
            #         max_tokens=max_tokens
            #     )

            insight = response.choices[0].message.content
            logger.info(f"LLM processing completed - Generated insight length: {len(insight)} characters")

            if provider.lower() == "openai":
                import tiktoken
                encoding = tiktoken.encoding_for_model(model)

                # Tokenize and count correctly
                input_tokens = len(encoding.encode(SYSTEM_PROMPT + user_query))
                output_tokens = len(encoding.encode(insight))
            
            elif provider.lower() == "gemini":
                input_tokens = int((len(user_query) + len(SYSTEM_PROMPT)) / 4)
                output_tokens = int(len(insight) / 4)

            logger.info(f"Token usage - Input: {input_tokens}, Output: {output_tokens}")

            result = {
                "survey_id": survey_id,
                "query": user_query,
                "insight": insight,
                "filtered_count": len(filtered_responses),
                "total_count": len(responses),
                "filters_applied": filters,
                "llm_config": llm_config,
                "input tokens": input_tokens,
                "output tokens": output_tokens
            }
            # Add warning if we used all responses instead of filtered ones
            if warning_message:
                result["warning"] = "No responses matched the specific filters. Using all available responses instead."
                result["using_all_responses"] = True
                logger.warning(f"Used all responses instead of filtered ones - Survey: {survey_id}")

            logger.info(f"Insight generation completed successfully - Survey: {survey_id}, Filtered: {len(filtered_responses)}, Total: {len(responses)}")
            return result

        except Exception as e:
            logger.error(f"Error in insight generation - Survey: {survey_id}, Query: {user_query}, Error: {str(e)}")
            return {
                "survey_id": survey_id,
                "query": user_query,
                "error": str(e),
                "insight": None,
                "filtered_count": len(filtered_responses),
                "total_count": len(responses),
                "filters_applied": filters,
                "llm_config": llm_config

            }

    def generate_insight_from_data(self,
                                  survey_id: str,
                                  user_query: str,
                                  responses: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Generate insights from provided survey responses based on a user query.

        This method allows generating insights from responses that are not stored in MongoDB.

        Args:
            survey_id: The ID of the survey to analyze.
            user_query: Natural language query describing the insights to generate.
            responses: List of response documents to analyze.

        Returns:
            Dictionary containing the generated insights and metadata.
        """
        if not responses:
            return {
                "survey_id": survey_id,
                "query": user_query,
                "error": "No responses provided",
                "insight": None,
                "filtered_count": 0,
                "total_count": 0
            }

        # Extract filters from the query
        filters = self.extract_filters_from_query(user_query)

        # Apply filters to get relevant responses
        filtered_responses = self.filter_responses(responses, filters)

        # Check if we have any responses after filtering
        if not filtered_responses:
            # If no responses match the filters, use all responses instead
            st.write(f"No responses match the filters. Using all {len(responses)} responses instead.")
            filtered_responses = responses

            # Create a warning message to include in the prompt
            warning_message = "Note: No responses matched the specific filters you mentioned. This analysis uses all available responses."
        else:
            warning_message = ""

        # Prepare data for OpenAI
        response_data = json.dumps(filtered_responses, cls=MongoJSONEncoder)

        # Generate insights using OpenAI
        SYSTEM_PROMPT = f"""
        You are an expert data analyst specializing in survey analysis.

        CRITICAL INSTRUCTIONS - READ CAREFULLY:

        NEVER use "NO RELEVANT DATA:" for queries that:
        - Ask for summaries, analysis, or insights about survey topics
        - Mention specific demographics (age, location, etc.)
        - Ask about specific aspects of the survey (teaching quality, satisfaction, etc.)
        - Request analysis of user responses or feedback
        - Are clearly related to the survey content

        ONLY use "NO RELEVANT DATA:" for:
        - Obvious test queries: "test", "testing", "hello", "sample", "demo"
        - Completely unrelated topics: asking about weather when survey is about education
        - When there are literally ZERO responses in the data (not just few responses)

        EXAMPLES OF VALID QUERIES (NEVER REJECT THESE):
        - "Summarize how users responded to questions about teaching quality"
        - "Analyze responses from users aged 18-25"
        - "What do people think about curriculum effectiveness"
        - "How satisfied are users with the service"
        - Any query asking for analysis, summary, or insights about survey content

        Now analyze the following survey responses and provide insights based on this query: "{user_query}"

        Survey ID: {survey_id}
        Number of responses: {len(filtered_responses)}
        Filters applied: {json.dumps(filters, indent=2)}
        {warning_message}

        Survey Responses:
        {response_data}

        REMEMBER: The query "{user_query}" appears to be a legitimate analytical request. Unless it's obviously a test query like "test" or "hello", provide a comprehensive analysis of the available data, even if the sample size is small.

        Include the following in your analysis:
        1. A clear summary of the key findings (noting sample size if small)
        2. Specific patterns or trends you observe in the data
        3. Notable outliers or exceptions
        4. Direct answers to any specific questions in the user query
        5. Relevant statistics or percentages to support your insights
        6. Limitations due to sample size if applicable

        Format your response in markdown with clear sections and bullet points where appropriate.
        Be concise but thorough, focusing on the most relevant insights for the query.
        """

        try:
            # Use the older OpenAI API format for compatibility
            response = openai.ChatCompletion.create(
                model="gpt-4",  # Using gpt-4 for compatibility
                messages=[
                    {"role": "system", "content": SYSTEM_PROMPT},
                    {"role": "user", "content": user_query}
                ],
                temperature=0.3,
                max_tokens=2000
            )

            insight = response.choices[0].message.content

            result = {
                "survey_id": survey_id,
                "query": user_query,
                "insight": insight,
                "filtered_count": len(filtered_responses),
                "total_count": len(responses),
                "filters_applied": filters,
                "generated_at": datetime.now().isoformat()
            }

            # Add warning if we used all responses instead of filtered ones
            if not filtered_responses and responses:
                result["warning"] = "No responses matched the specific filters. Using all available responses instead."
                result["using_all_responses"] = True

            return result

        except Exception as e:
            return {
                "survey_id": survey_id,
                "query": user_query,
                "error": str(e),
                "insight": None,
                "filtered_count": len(filtered_responses),
                "total_count": len(responses),
                "filters_applied": filters
            }

def main():
    """Main function to run the script from command line."""
    parser = argparse.ArgumentParser(description='Generate insights from survey responses.')
    parser.add_argument('--survey_id', required=True, help='Survey ID to analyze')
    parser.add_argument('--query', required=True, help='Query describing the insights to generate')
    parser.add_argument('--mongo_uri', help='MongoDB connection URI')
    parser.add_argument('--db_name', default='survey_dev_db', help='MongoDB database name')
    parser.add_argument('--collection_name', default='responses', help='MongoDB collection name')
    parser.add_argument('--output', help='Output file path (JSON)')

    args = parser.parse_args()

    try:
        # Initialize the insight generator
        generator = SurveyInsightGenerator(
            mongo_uri=args.mongo_uri,
            db_name=args.db_name,
            collection_name=args.collection_name
        )

        # Generate insights
        result = generator.generate_insight(args.survey_id, args.query)

        # Output results
        if args.output:
            with open(args.output, 'w') as f:
                json.dump(result, f, indent=2)
            print(f"Results saved to {args.output}")
        else:
            print(json.dumps(result, indent=2))

    except Exception as e:
        print(f"Error: {str(e)}")
        return 1

    return 0

if __name__ == "__main__":
    main()
