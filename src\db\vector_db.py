"""
Vector database for storing embeddings.
This is a simple file-based database that stores vector embeddings in JSON format.
"""

import json
import os
import uuid
from typing import List, Dict, Any, Optional
from datetime import datetime
import numpy as np

# Define the path to the vector database file
DB_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "data")
VECTORS_FILE = os.path.join(DB_DIR, "vector_embeddings.json")

# Ensure the data directory exists
os.makedirs(DB_DIR, exist_ok=True)

class VectorDatabase:
    """Simple file-based database for storing vector embeddings."""

    def __init__(self, file_path: str = VECTORS_FILE):
        """Initialize the database with the specified file path."""
        self.file_path = file_path
        self._ensure_file_exists()

    def _ensure_file_exists(self) -> None:
        """Ensure the database file exists, creating it if necessary."""
        if not os.path.exists(self.file_path):
            # Create an empty database structure
            with open(self.file_path, 'w') as f:
                json.dump({
                    "vectors": [],
                    "last_updated": datetime.now().isoformat()
                }, f, indent=2)

    def _read_data(self) -> Dict[str, Any]:
        """Read the current data from the database file."""
        try:
            with open(self.file_path, 'r') as f:
                return json.load(f)
        except json.JSONDecodeError:
            # If the file is corrupted, create a new empty database
            return {
                "vectors": [],
                "last_updated": datetime.now().isoformat()
            }

    def _write_data(self, data: Dict[str, Any]) -> None:
        """Write data to the database file."""
        # Update the last_updated timestamp
        data["last_updated"] = datetime.now().isoformat()

        with open(self.file_path, 'w') as f:
            json.dump(data, f, indent=2)

    def add_vector(self, vector: List[float], metadata: Dict[str, Any] = None) -> str:
        """
        Add a new vector embedding to the database.

        Args:
            vector: The vector embedding to add
            metadata: Additional metadata to store with the vector

        Returns:
            The ID of the newly added vector
        """
        # Read the current data
        data = self._read_data()

        # Generate a unique ID for the vector
        vector_id = str(uuid.uuid4())

        # Create the vector entry
        vector_entry = {
            "id": vector_id,
            "vector": vector,
            "metadata": metadata or {},
            "created_at": datetime.now().isoformat()
        }

        # Add the vector to the list
        data["vectors"].append(vector_entry)

        # Write the updated data back to the file
        self._write_data(data)

        return vector_id

    def get_vector(self, vector_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a vector embedding by ID.

        Args:
            vector_id: The ID of the vector to retrieve

        Returns:
            The vector entry if found, None otherwise
        """
        # Read the current data
        data = self._read_data()

        # Find the vector with the specified ID
        for vector in data["vectors"]:
            if vector.get("id") == vector_id:
                return vector

        return None

    def search_similar_vectors(self, query_vector: List[float], top_k: int = 5) -> List[Dict[str, Any]]:
        """
        Search for vectors similar to the query vector.

        Args:
            query_vector: The vector to search for
            top_k: The number of results to return

        Returns:
            A list of the top_k most similar vectors
        """
        # Read the current data
        data = self._read_data()

        # Convert the query vector to a numpy array
        query_np = np.array(query_vector)

        # Calculate the similarity for each vector
        results = []
        for vector_entry in data["vectors"]:
            vector = vector_entry.get("vector", [])
            if not vector:
                continue

            # Convert the vector to a numpy array
            vector_np = np.array(vector)

            # Calculate cosine similarity
            similarity = self._cosine_similarity(query_np, vector_np)

            # Add the result
            results.append({
                "id": vector_entry.get("id"),
                "similarity": similarity,
                "metadata": vector_entry.get("metadata", {}),
                "created_at": vector_entry.get("created_at")
            })

        # Sort by similarity (highest first)
        results.sort(key=lambda x: x["similarity"], reverse=True)

        # Return the top_k results
        return results[:top_k]

    def _cosine_similarity(self, vec1: np.ndarray, vec2: np.ndarray) -> float:
        """
        Calculate the cosine similarity between two vectors.

        Args:
            vec1: The first vector
            vec2: The second vector

        Returns:
            The cosine similarity between the vectors
        """
        dot_product = np.dot(vec1, vec2)
        norm1 = np.linalg.norm(vec1)
        norm2 = np.linalg.norm(vec2)

        if norm1 == 0 or norm2 == 0:
            return 0.0

        return dot_product / (norm1 * norm2)

    def delete_vector(self, vector_id: str) -> bool:
        """
        Delete a vector embedding.

        Args:
            vector_id: The ID of the vector to delete

        Returns:
            True if the vector was deleted, False otherwise
        """
        # Read the current data
        data = self._read_data()

        # Find the vector with the specified ID
        for i, vector in enumerate(data["vectors"]):
            if vector.get("id") == vector_id:
                # Remove the vector
                del data["vectors"][i]

                # Write the updated data back to the file
                self._write_data(data)

                return True

        return False

# Create a singleton instance of the database
vector_db = VectorDatabase()
