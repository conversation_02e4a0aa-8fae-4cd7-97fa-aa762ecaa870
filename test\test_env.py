#!/usr/bin/env python3
"""
Environment Variables Test Script
Run this to verify your environment variables are properly configured
"""

import os
from dotenv import load_dotenv

def test_environment_variables():
    """Test if all required environment variables are loaded"""
    
    print("🔍 Testing Environment Variables...")
    print("=" * 50)
    
    # Load environment variables from .env file
    load_dotenv()
    
    # Define required environment variables
    required_vars = {
        "OPENAI_API_KEY": "OpenAI API Key",
        "MONGO_URI": "MongoDB Connection URI", 
        "GOOGLE_API_KEY": "Google API Key"
    }
    
    optional_vars = {
        "DATABASE_URL": "PostgreSQL Database URL",
        "MONGODB_DB_NAME": "MongoDB Database Name"
    }
    
    all_good = True
    
    print("\n📋 Required Environment Variables:")
    print("-" * 40)
    
    for var_name, description in required_vars.items():
        value = os.environ.get(var_name)
        if value:
            # Mask sensitive information
            if len(value) > 10:
                masked_value = value[:6] + "..." + value[-4:]
            else:
                masked_value = value[:3] + "..."
            print(f"✅ {var_name}: {masked_value}")
        else:
            print(f"❌ {var_name}: NOT FOUND")
            all_good = False
    
    print("\n📋 Optional Environment Variables:")
    print("-" * 40)
    
    for var_name, description in optional_vars.items():
        value = os.environ.get(var_name)
        if value:
            if len(value) > 10:
                masked_value = value[:6] + "..." + value[-4:]
            else:
                masked_value = value[:3] + "..."
            print(f"✅ {var_name}: {masked_value}")
        else:
            print(f"⚠️  {var_name}: NOT SET (optional)")
    
    print("\n" + "=" * 50)
    
    if all_good:
        print("🎉 All required environment variables are configured!")
        print("✅ You can now run: streamlit run app_oop.py")
    else:
        print("❌ Some required environment variables are missing.")
        print("📖 Please check the ENV_SETUP_GUIDE.md for setup instructions.")
    
    return all_good

def test_openai_connection():
    """Test OpenAI API connection"""
    try:
        from openai import OpenAI
        
        api_key = os.environ.get("OPENAI_API_KEY")
        if not api_key:
            print("❌ Cannot test OpenAI connection: API key not found")
            return False
        
        print("\n🔗 Testing OpenAI API Connection...")
        print("-" * 40)
        
        client = OpenAI(api_key=api_key)
        
        # Test with a simple completion
        response = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[{"role": "user", "content": "Say 'API test successful'"}],
            max_tokens=10
        )
        
        if response.choices[0].message.content:
            print("✅ OpenAI API connection successful!")
            print(f"📝 Response: {response.choices[0].message.content}")
            return True
        else:
            print("❌ OpenAI API connection failed: No response")
            return False
            
    except Exception as e:
        print(f"❌ OpenAI API connection failed: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 Environment Variables Test")
    print("=" * 50)
    
    # Test environment variables
    env_ok = test_environment_variables()
    
    # Test OpenAI connection if env vars are OK
    if env_ok:
        api_ok = test_openai_connection()
        
        if api_ok:
            print("\n🎉 All tests passed! Your environment is ready.")
        else:
            print("\n⚠️  Environment variables are set but API connection failed.")
            print("   Please check your API key validity and internet connection.")
    
    print("\n📖 For more help, see ENV_SETUP_GUIDE.md")
