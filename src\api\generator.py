from fastapi import APIRouter, HTTPException, status, Body
from app.models.survey import SurveyRequest, SurveyResponse, SurveyResponseSubmission, StoredSurveyResponse
from app.services.llm_client import generate_questions_from_llm
from app.services.session_manager import session_manager
from app.services.response_manager import response_manager
from app.utils.helpers import extract_num_questions
from app.utils.examples import examples
from app.exceptions import BadRequestException, LLMException, NotFoundException
import logging
from typing import List, Dict, Any
from app.api.sessions import *

# Configure logger
logger = logging.getLogger(__name__)

router = APIRouter()

@router.post("/",
             response_model=SurveyResponse,
             summary="Generate survey questions",
             description="Generate survey questions based on context and language",
             response_description="Returns generated questions with session information")
async def generate_questions(
    request: SurveyRequest = Body(..., examples=[examples["question_generator"]["value"]])
):
    try:
        # Validate request
        if not request.context or not request.language:
            field = "context" if not request.context else "language"
            raise BadRequestException(
                detail="Both context and language are required.",
                field=field,
                suggestion="Please provide both the context and language fields in your request."
            )

        # Check if this is a continued session
        is_continued_session = False
        session = None
        messages = None

        if request.session_id:
            # Try to get the existing session
            session = session_manager.get_session(request.session_id)
            if not session:
                raise NotFoundException(
                    detail=f"Session with ID {request.session_id} not found or expired.",
                    field="session_id",
                    suggestion="Please start a new session by omitting the session_id field."
                )

            # Use the session's language if not specified in the request
            language = request.language or session["language"]
            messages = session["messages"]
            is_continued_session = True
            logger.info(f"Continuing session {request.session_id}")
        else:
            # Create a new session
            language = request.language
            logger.info("Starting new session")

        # Extract number of questions from context using LLM
        try:
            num_questions = extract_num_questions(request.context)
            logger.info(f"Generating {num_questions} questions in {language}")
        except Exception as e:
            # If extraction fails, use default value
            num_questions = 5
            logger.warning(f"Failed to extract number of questions: {e}. Using default: {num_questions}")

        # Generate questions using LLM
        questions = generate_questions_from_llm(
            context=request.context,
            language=language,
            num_questions=num_questions,
            messages=messages
        )

        # Validate LLM response
        if not questions or not isinstance(questions, list):
            raise LLMException(
                detail="LLM response did not return valid questions.",
                field="questions",
                suggestion="Please try again with a different context or contact support if the issue persists."
            )

        # Create or update session
        if is_continued_session:
            # Update existing session
            session_id = request.session_id
            session_manager.update_session(session_id, request.context, questions)
        else:
            # Create new session
            session_id = session_manager.create_session(request.context, language)
            session_manager.update_session(session_id, "", questions)  # Update with generated questions

        # Return response
        return SurveyResponse(
            session_id=session_id,
            questions=questions,
            is_continued_session=is_continued_session,
            metadata={
                "language": language,
                "num_questions": num_questions,
                "model_used": "gpt-4"
            }
        )
    except (BadRequestException, LLMException):
        # Re-raise our custom exceptions
        raise
    except Exception as e:
        # Convert unexpected exceptions to LLMException
        logger.exception("Unexpected error in generate_questions")
        raise LLMException(
            detail=f"Survey generation failed: {str(e)}",
            field="",
            suggestion="Please try again later or contact support if the issue persists."
        )

@router.get("/all_sessions",
            response_model=SessionsList,
            summary="List all sessions",
            description="List all active sessions",
            response_description="Returns a list of all active sessions")
async def list_sessions():
    """
    List all active sessions.

    Example:
    ```
    GET /sessions/
    ```

    Example response:
    ```json
    {
        "sessions": [
            {
                "session_id": "session-12345678",
                "language": "English",
                "created_at": 1653123456.789,
                "last_accessed": 1653123456.789,
                "question_count": 5,
                "context_summary": "Create a customer satisfaction survey..."
            },
            {
                "session_id": "session-87654321",
                "language": "Spanish",
                "created_at": 1653123456.789,
                "last_accessed": 1653123456.789,
                "question_count": 3,
                "context_summary": "Crear una encuesta de satisfacción..."
            }
        ],
        "count": 2
    }
    ```
    """
    # Clean expired sessions first
    session_manager.clean_expired_sessions()

    # Get all active sessions
    sessions_info = []
    for session_id, session in session_manager.sessions.items():
        # Create a summary of the context (first 100 characters)
        context_summary = session["context"][:100]
        if len(session["context"]) > 100:
            context_summary += "..."

        sessions_info.append(SessionInfo(
            session_id=session_id,
            language=session["language"],
            created_at=session["created_at"],
            last_accessed=session["last_accessed"],
            question_count=len(session["questions"]),
            context_summary=context_summary
        ))

    return SessionsList(
        sessions=sessions_info,
        count=len(sessions_info)
    )

@router.get("/{session_id}",
            response_model=SessionInfo,
            summary="Get session information",
            description="Get information about a specific session",
            response_description="Returns session details including language, creation time, and question count")
async def get_session(session_id: str = Path(..., description="The session ID to retrieve")):
    """
    Get information about a specific session.

    Example:
    ```
    GET /sessions/session-12345678
    ```

    Example response:
    ```json
    {
        "session_id": "session-12345678",
        "language": "English",
        "created_at": 1653123456.789,
        "last_accessed": 1653123456.789,
        "question_count": 5,
        "context_summary": "Create a customer satisfaction survey for a software product..."
    }
    ```
    """
    session = session_manager.get_session(session_id)
    if not session:
        raise NotFoundException(
            detail=f"Session with ID {session_id} not found or expired.",
            field="session_id",
            suggestion="Please check the session ID or start a new session."
        )

    # Create a summary of the context (first 100 characters)
    context_summary = session["context"][:100]
    if len(session["context"]) > 100:
        context_summary += "..."

    return SessionInfo(
        session_id=session_id,
        language=session["language"],
        created_at=session["created_at"],
        last_accessed=session["last_accessed"],
        question_count=len(session["questions"]),
        context_summary=context_summary
    )

class SessionsList(BaseModel):
    """List of active sessions."""
    sessions: List[SessionInfo] = Field(description="List of active sessions")
    count: int = Field(description="Total number of active sessions")


# @router.post("/responses",
#              response_model=Dict[str, Any],
#              status_code=status.HTTP_201_CREATED,
#              summary="Submit survey responses",
#              description="Submit responses to survey questions with demographic information",
#              response_description="Returns confirmation with response ID",
#              tags=["2. Response Generator"])
# async def submit_survey_response(
#     response: SurveyResponseSubmission = Body(..., examples=[examples["response_generator"]["value"]])
# ):
#     """
#     Submit a survey response.

#     This endpoint allows users to submit their answers to survey questions.
#     The response includes demographic information (age, region) and a list of question-answer pairs.

#     Example:
#     ```json
#     {
#         "survey_id": "session-12345678",
#         "user_id": "user-987654",
#         "age": 30,
#         "region": "New York",
#         "answers": [
#             {
#                 "question": "How satisfied are you with the product?",
#                 "answer": "Very satisfied"
#             }
#         ]
#     }
#     ```
#     """
#     try:
#         # Validate the response
#         if not response.survey_id:
#             raise HTTPException(
#                 status_code=status.HTTP_400_BAD_REQUEST,
#                 detail="Survey ID is required"
#             )

#         if not response.user_id:
#             raise HTTPException(
#                 status_code=status.HTTP_400_BAD_REQUEST,
#                 detail="User ID is required"
#             )

#         if not response.answers or len(response.answers) == 0:
#             raise HTTPException(
#                 status_code=status.HTTP_400_BAD_REQUEST,
#                 detail="At least one answer is required"
#             )

#         # Store the response
#         response_id = response_manager.store_response(response)

#         # Return the response ID
#         return {
#             "id": response_id,
#             "message": "Survey response submitted successfully",
#             "survey_id": response.survey_id,
#             "user_id": response.user_id,
#             "num_answers": len(response.answers)
#         }
#     except HTTPException:
#         # Re-raise HTTP exceptions
#         raise
#     except Exception as e:
#         # Log the error
#         logger.exception("Error submitting survey response")

#         # Return a 500 error
#         raise HTTPException(
#             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
#             detail=f"Failed to submit survey response: {str(e)}"
#         )

# @router.get("/responses/{response_id}",
#             response_model=Dict[str, Any],
#             summary="Get a survey response",
#             description="Retrieve a specific survey response by its ID",
#             response_description="Returns the complete survey response",
#             tags=["2. Response Generator"])
# async def get_survey_response(response_id: str):
#     """
#     Get a survey response by ID.

#     This endpoint retrieves a specific survey response by its ID.

#     Example:
#     ```
#     GET /generate/responses/response-12345678
#     ```
#     """
#     try:
#         # Get the response
#         response = response_manager.get_response(response_id)

#         # Check if the response exists
#         if not response:
#             raise HTTPException(
#                 status_code=status.HTTP_404_NOT_FOUND,
#                 detail=f"Survey response with ID {response_id} not found"
#             )

#         # Return the response
#         return response
#     except HTTPException:
#         # Re-raise HTTP exceptions
#         raise
#     except Exception as e:
#         # Log the error
#         logger.exception(f"Error retrieving survey response {response_id}")

#         # Return a 500 error
#         raise HTTPException(
#             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
#             detail=f"Failed to retrieve survey response: {str(e)}"
#         )

# @router.get("/surveys/{survey_id}/responses",
#             response_model=List[Dict[str, Any]],
#             summary="Get all survey responses",
#             description="Retrieve all responses for a specific survey",
#             response_description="Returns a list of all survey responses",
#             tags=["2. Response Generator"])
# async def get_survey_responses(survey_id: str):
#     """
#     Get all responses for a specific survey.

#     This endpoint retrieves all responses for a specific survey.

#     Example:
#     ```
#     GET /generate/surveys/session-12345678/responses
#     ```
#     """
#     try:
#         # Get the responses
#         responses = response_manager.get_responses_by_survey(survey_id)

#         # Return the responses
#         return responses
#     except Exception as e:
#         # Log the error
#         logger.exception(f"Error retrieving responses for survey {survey_id}")

#         # Return a 500 error
#         raise HTTPException(
#             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
#             detail=f"Failed to retrieve survey responses: {str(e)}"
#         )